import Foundation
import SwiftUI

// MARK: - Cognitive Analysis Service Protocol

@available(iOS 18.0, *)
protocol CognitiveAnalysisServiceProtocol {
    func analyzeTaskPerformance(_ completion: TaskCompletion) async -> CognitiveAnalysis
    func identifyPatterns(_ data: [TaskCompletion]) async -> CognitivePatterns
    func suggestOptimizations(_ analysis: CognitiveAnalysis) async -> [CognitiveOptimization]
    func suggestTaskTiming(_ task: AITask, for user: UserProfile) async -> TaskTiming
    func trackCognitiveLoad(_ level: CognitiveLoadLevel, context: String) async
    func generateCognitiveInsights(for userId: UUID) async throws -> CognitiveInsights
}

// MARK: - Cognitive Analysis Service Implementation

@available(iOS 18.0, *)
@MainActor
class CognitiveAnalysisService: CognitiveAnalysisServiceProtocol {
    // MARK: - Properties

    private let userDefaults = UserDefaults.standard
    private var cognitiveLoadHistory: [CognitiveLoadEntry] = []
    private var taskPerformanceHistory: [TaskPerformanceEntry] = []

    // MARK: - Initialization

    init() {
        loadHistoricalData()
    }

    // MARK: - Service Methods

    func analyzeTaskPerformance(_ completion: TaskCompletion) async -> CognitiveAnalysis {
        // Record the performance entry
        let performanceEntry = TaskPerformanceEntry(
            taskId: completion.taskId,
            completionTime: completion.completionTime,
            cognitiveLoadDuring: completion.cognitiveLoadDuring,
            interruptions: completion.interruptions,
            timestamp: completion.completedAt
        )

        taskPerformanceHistory.append(performanceEntry)
        saveHistoricalData()

        // Analyze the performance
        let analysis = CognitiveAnalysis(
            id: UUID(),
            taskId: completion.taskId,
            userId: completion.userId,
            analysisDate: Date(),
            cognitiveLoadPattern: analyzeCognitiveLoadPattern(completion),
            performanceMetrics: calculatePerformanceMetrics(completion),
            attentionSpan: calculateAttentionSpan(completion),
            focusQuality: calculateFocusQuality(completion),
            distractionLevel: calculateDistractionLevel(completion),
            optimalWorkingConditions: identifyOptimalConditions(completion),
            recommendations: generatePerformanceRecommendations(completion),
            confidenceScore: calculateAnalysisConfidence(completion)
        )

        return analysis
    }

    func identifyPatterns(_ data: [TaskCompletion]) async -> CognitivePatterns {
        let patterns = CognitivePatterns(
            id: UUID(),
            userId: data.first?.userId ?? UUID(),
            analysisDate: Date(),
            timeOfDayPatterns: analyzeTimeOfDayPatterns(data),
            taskTypePatterns: analyzeTaskTypePatterns(data),
            cognitiveLoadPatterns: analyzeCognitiveLoadPatterns(data),
            productivityRhythms: analyzeProductivityRhythms(data),
            breakPatterns: analyzeBreakPatterns(data),
            focusPatterns: analyzeFocusPatterns(data),
            distractionTriggers: identifyDistractionTriggers(data),
            optimalConditions: identifyOptimalWorkingConditions(data)
        )

        return patterns
    }

    func suggestOptimizations(_ analysis: CognitiveAnalysis) async -> [CognitiveOptimization] {
        var optimizations: [CognitiveOptimization] = []

        // Analyze cognitive load patterns
        if analysis.cognitiveLoadPattern.averageLevel > 0.7 {
            optimizations.append(CognitiveOptimization(
                id: UUID(),
                type: .cognitiveLoadReduction,
                title: "Reduce Cognitive Load",
                description: "Your cognitive load is consistently high. Consider breaking tasks into smaller chunks.",
                implementation: .taskBreakdown,
                expectedImpact: .high,
                priority: .high,
                estimatedEffort: .medium
            ))
        }

        // Analyze attention span
        if analysis.attentionSpan < 1_200 { // Less than 20 minutes
            optimizations.append(CognitiveOptimization(
                id: UUID(),
                type: .attentionImprovement,
                title: "Improve Attention Span",
                description: "Your attention span could be improved with focused practice.",
                implementation: .focusTraining,
                expectedImpact: .medium,
                priority: .medium,
                estimatedEffort: .high
            ))
        }

        // Analyze distraction levels
        if analysis.distractionLevel > 0.6 {
            optimizations.append(CognitiveOptimization(
                id: UUID(),
                type: .distractionReduction,
                title: "Minimize Distractions",
                description: "High distraction levels detected. Consider environmental modifications.",
                implementation: .environmentalChanges,
                expectedImpact: .high,
                priority: .high,
                estimatedEffort: .low
            ))
        }

        return optimizations
    }

    func suggestTaskTiming(_ task: AITask, for user: UserProfile) async -> TaskTiming {
        // Analyze user's historical performance patterns
        let userPatterns = await identifyPatterns(getUserTaskCompletions(user.id))

        // Determine optimal timing based on patterns
        let optimalTimeOfDay = determineOptimalTimeOfDay(for: task, patterns: userPatterns)
        let estimatedDuration = estimateTaskDuration(task, basedOn: userPatterns)
        let recommendedBreaks = calculateRecommendedBreaks(for: estimatedDuration)

        return TaskTiming(
            id: UUID(),
            taskId: task.id,
            userId: user.id,
            optimalStartTime: optimalTimeOfDay,
            estimatedDuration: estimatedDuration,
            recommendedBreaks: recommendedBreaks,
            cognitiveLoadPrediction: predictCognitiveLoad(for: task, user: user),
            confidenceLevel: 0.75,
            createdAt: Date()
        )
    }

    func trackCognitiveLoad(_ level: CognitiveLoadLevel, context: String) async {
        let entry = CognitiveLoadEntry(
            level: level,
            context: context,
            timestamp: Date()
        )

        cognitiveLoadHistory.append(entry)

        // Keep only last 1000 entries to manage memory
        if cognitiveLoadHistory.count > 1_000 {
            cognitiveLoadHistory.removeFirst(cognitiveLoadHistory.count - 1_000)
        }

        saveHistoricalData()
    }

    func generateCognitiveInsights(for userId: UUID) async throws -> CognitiveInsights {
        let userCompletions = getUserTaskCompletions(userId)
        let patterns = await identifyPatterns(userCompletions)

        let insights = CognitiveInsights(
            id: UUID(),
            userId: userId,
            generatedAt: Date(),
            cognitiveStrengths: identifyCognitiveStrengths(patterns),
            cognitiveWeaknesses: identifyCognitiveWeaknesses(patterns),
            optimalWorkingHours: patterns.timeOfDayPatterns.optimalHours,
            recommendedTaskTypes: patterns.taskTypePatterns.mostEffective,
            suggestedBreakFrequency: patterns.breakPatterns.optimalFrequency,
            personalizedStrategies: generatePersonalizedStrategies(patterns),
            progressMetrics: calculateProgressMetrics(userCompletions),
            nextReviewDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()) ?? Date()
        )

        return insights
    }

    // MARK: - Private Helper Methods

    private func loadHistoricalData() {
        // Load cognitive load history
        if let loadData = userDefaults.data(forKey: "cognitive_load_history"),
           let history = try? JSONDecoder().decode([CognitiveLoadEntry].self, from: loadData) {
            cognitiveLoadHistory = history
        }

        // Load task performance history
        if let performanceData = userDefaults.data(forKey: "task_performance_history"),
           let history = try? JSONDecoder().decode([TaskPerformanceEntry].self, from: performanceData) {
            taskPerformanceHistory = history
        }
    }

    private func saveHistoricalData() {
        // Save cognitive load history
        if let loadData = try? JSONEncoder().encode(cognitiveLoadHistory) {
            userDefaults.set(loadData, forKey: "cognitive_load_history")
        }

        // Save task performance history
        if let performanceData = try? JSONEncoder().encode(taskPerformanceHistory) {
            userDefaults.set(performanceData, forKey: "task_performance_history")
        }
    }

    private func analyzeCognitiveLoadPattern(_ completion: TaskCompletion) -> CognitiveLoadPattern {
        CognitiveLoadPattern(
            averageLevel: completion.cognitiveLoadDuring,
            peakLevel: completion.cognitiveLoadDuring * 1.2,
            variability: 0.3,
            duration: completion.completionTime
        )
    }

    private func calculatePerformanceMetrics(_ completion: TaskCompletion) -> PerformanceMetrics {
        PerformanceMetrics(
            completionTime: completion.completionTime,
            accuracy: completion.accuracy,
            efficiency: calculateEfficiency(completion),
            qualityScore: completion.qualityScore
        )
    }

    private func calculateAttentionSpan(_ completion: TaskCompletion) -> TimeInterval {
        // Calculate based on interruptions and task duration
        let baseAttentionSpan = completion.completionTime
        let interruptionPenalty = Double(completion.interruptions) * 300 // 5 minutes per interruption
        return max(baseAttentionSpan - interruptionPenalty, 300) // Minimum 5 minutes
    }

    private func calculateFocusQuality(_ completion: TaskCompletion) -> Double {
        // Calculate focus quality based on interruptions and cognitive load stability
        let interruptionFactor = 1.0 - (Double(completion.interruptions) * 0.1)
        let cognitiveLoadFactor = 1.0 - abs(completion.cognitiveLoadDuring - 0.5) // Optimal around 0.5
        return max((interruptionFactor + cognitiveLoadFactor) / 2.0, 0.0)
    }

    // MARK: - Distraction, conditions, and recommendations methods moved to CognitiveAnalysisServiceHelpers.swift

    private func calculateAnalysisConfidence(_ completion: TaskCompletion) -> Double {
        // Base confidence on data quality and completeness
        var confidence = 0.5

        if completion.interruptions >= 0 { confidence += 0.1 }
        if completion.cognitiveLoadDuring > 0 { confidence += 0.2 }
        if completion.accuracy > 0 { confidence += 0.2 }

        return min(confidence, 1.0)
    }

    // MARK: - Time of day and task type analysis methods moved to CognitiveAnalysisServiceHelpers.swift

    // MARK: - Pattern analysis methods moved to CognitiveAnalysisServiceHelpers.swift

    private func getUserTaskCompletions(_ userId: UUID) -> [TaskCompletion] {
        // This would typically fetch from a repository
        // For now, return empty array as placeholder
        []
    }

    private func calculateEfficiency(_ completion: TaskCompletion) -> Double {
        completion.estimatedDuration / completion.completionTime
    }

    private func determineOptimalTimeOfDay(for task: AITask, patterns: CognitivePatterns) -> Date {
        let calendar = Calendar.current
        let today = Date()
        let optimalHour = patterns.timeOfDayPatterns.optimalHours.first ?? 10

        return calendar.date(bySettingHour: optimalHour, minute: 0, second: 0, of: today) ?? today
    }

    private func estimateTaskDuration(_ task: AITask, basedOn patterns: CognitivePatterns) -> TimeInterval {
        // Base estimation on task complexity and historical patterns
        let baseTime: TimeInterval = 1_800 // 30 minutes
        let complexityMultiplier = task.complexity.rawValue
        return baseTime * Double(complexityMultiplier)
    }

    private func calculateRecommendedBreaks(for duration: TimeInterval) -> [TimeInterval] {
        let breakInterval: TimeInterval = 1_800 // Every 30 minutes
        var breaks: [TimeInterval] = []
        var currentTime: TimeInterval = breakInterval

        while currentTime < duration {
            breaks.append(currentTime)
            currentTime += breakInterval
        }

        return breaks
    }

    private func predictCognitiveLoad(for task: AITask, user: UserProfile) -> Double {
        // Predict based on task complexity and user's cognitive preferences
        let baseLoad = Double(task.complexity.rawValue) * 0.2
        let userAdjustment = user.cognitivePreferences.preferredCognitiveLoad
        return min(baseLoad + userAdjustment, 1.0)
    }

    // MARK: - Cognitive analysis helper methods moved to CognitiveAnalysisServiceHelpers.swift
}

// MARK: - Supporting Types

@available(iOS 18.0, *)
struct CognitiveLoadEntry: Codable {
    let level: CognitiveLoadLevel
    let context: String
    let timestamp: Date
}

@available(iOS 18.0, *)
struct TaskPerformanceEntry: Codable {
    let taskId: UUID
    let completionTime: TimeInterval
    let cognitiveLoadDuring: Double
    let interruptions: Int
    let timestamp: Date
}
