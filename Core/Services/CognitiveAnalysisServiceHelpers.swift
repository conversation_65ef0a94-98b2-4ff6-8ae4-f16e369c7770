import Foundation

// MARK: - Cognitive Analysis Service Helper Methods
// Extracted helper methods to reduce main CognitiveAnalysisService file size

@available(iOS 18.0, *)
extension CognitiveAnalysisService {
    
    // MARK: - Analysis Calculation Helpers
    
    func calculateCognitiveLoadScore(from metrics: CognitiveMetrics) -> Double {
        let taskComplexityWeight = 0.3
        let timeSpentWeight = 0.2
        let errorRateWeight = 0.25
        let fatigueWeight = 0.25
        
        let complexityScore = Double(metrics.taskComplexity) / 10.0
        let timeScore = min(metrics.timeSpent / 3600.0, 1.0) // Normalize to 1 hour max
        let errorScore = 1.0 - min(metrics.errorRate, 1.0)
        let fatigueScore = 1.0 - min(metrics.fatigueLevel, 1.0)
        
        return (complexityScore * taskComplexityWeight) +
               (timeScore * timeSpentWeight) +
               (errorScore * errorRateWeight) +
               (fatigueScore * fatigueWeight)
    }
    
    func calculateAttentionScore(from metrics: AttentionMetrics) -> Double {
        let focusDurationWeight = 0.4
        let distractionCountWeight = 0.3
        let taskSwitchingWeight = 0.3
        
        let focusScore = min(metrics.focusDuration / 1800.0, 1.0) // Normalize to 30 minutes
        let distractionScore = 1.0 - min(Double(metrics.distractionCount) / 10.0, 1.0)
        let switchingScore = 1.0 - min(Double(metrics.taskSwitches) / 5.0, 1.0)
        
        return (focusScore * focusDurationWeight) +
               (distractionScore * distractionCountWeight) +
               (switchingScore * taskSwitchingWeight)
    }
    
    func calculateExecutiveFunctionScore(from metrics: ExecutiveFunctionMetrics) -> Double {
        let planningWeight = 0.35
        let workingMemoryWeight = 0.35
        let flexibilityWeight = 0.3
        
        return (metrics.planningSkills * planningWeight) +
               (metrics.workingMemory * workingMemoryWeight) +
               (metrics.cognitiveFlexibility * flexibilityWeight)
    }
    
    // MARK: - Pattern Recognition Helpers
    
    func identifyAttentionPatterns(from history: [AttentionMetrics]) -> [AttentionPattern] {
        var patterns: [AttentionPattern] = []
        
        // Identify time-of-day patterns
        let morningMetrics = history.filter { Calendar.current.component(.hour, from: $0.timestamp) < 12 }
        let afternoonMetrics = history.filter { 
            let hour = Calendar.current.component(.hour, from: $0.timestamp)
            return hour >= 12 && hour < 18
        }
        let eveningMetrics = history.filter { Calendar.current.component(.hour, from: $0.timestamp) >= 18 }
        
        if !morningMetrics.isEmpty {
            let avgMorningFocus = morningMetrics.map { $0.focusDuration }.reduce(0, +) / Double(morningMetrics.count)
            patterns.append(AttentionPattern(
                type: .timeOfDay,
                timeRange: "Morning",
                averageFocus: avgMorningFocus,
                confidence: calculatePatternConfidence(morningMetrics)
            ))
        }
        
        if !afternoonMetrics.isEmpty {
            let avgAfternoonFocus = afternoonMetrics.map { $0.focusDuration }.reduce(0, +) / Double(afternoonMetrics.count)
            patterns.append(AttentionPattern(
                type: .timeOfDay,
                timeRange: "Afternoon", 
                averageFocus: avgAfternoonFocus,
                confidence: calculatePatternConfidence(afternoonMetrics)
            ))
        }
        
        if !eveningMetrics.isEmpty {
            let avgEveningFocus = eveningMetrics.map { $0.focusDuration }.reduce(0, +) / Double(eveningMetrics.count)
            patterns.append(AttentionPattern(
                type: .timeOfDay,
                timeRange: "Evening",
                averageFocus: avgEveningFocus,
                confidence: calculatePatternConfidence(eveningMetrics)
            ))
        }
        
        return patterns
    }
    
    func identifyExecutiveFunctionPatterns(from history: [ExecutiveFunctionMetrics]) -> [ExecutiveFunctionPattern] {
        var patterns: [ExecutiveFunctionPattern] = []
        
        // Identify planning skill trends
        let planningTrend = calculateTrend(history.map { $0.planningSkills })
        if abs(planningTrend) > 0.1 {
            patterns.append(ExecutiveFunctionPattern(
                type: .planning,
                trend: planningTrend > 0 ? .improving : .declining,
                magnitude: abs(planningTrend),
                confidence: 0.8
            ))
        }
        
        // Identify working memory trends
        let memoryTrend = calculateTrend(history.map { $0.workingMemory })
        if abs(memoryTrend) > 0.1 {
            patterns.append(ExecutiveFunctionPattern(
                type: .workingMemory,
                trend: memoryTrend > 0 ? .improving : .declining,
                magnitude: abs(memoryTrend),
                confidence: 0.8
            ))
        }
        
        // Identify cognitive flexibility trends
        let flexibilityTrend = calculateTrend(history.map { $0.cognitiveFlexibility })
        if abs(flexibilityTrend) > 0.1 {
            patterns.append(ExecutiveFunctionPattern(
                type: .flexibility,
                trend: flexibilityTrend > 0 ? .improving : .declining,
                magnitude: abs(flexibilityTrend),
                confidence: 0.8
            ))
        }
        
        return patterns
    }
    
    // MARK: - Recommendation Generation Helpers
    
    func generateCognitiveLoadRecommendations(from analysis: CognitiveLoadAnalysis) -> [CognitiveRecommendation] {
        var recommendations: [CognitiveRecommendation] = []
        
        if analysis.currentLoad > 0.8 {
            recommendations.append(CognitiveRecommendation(
                type: .breakSuggestion,
                title: "Take an Immediate Break",
                description: "Your cognitive load is very high. Take a 10-15 minute break to reset.",
                priority: .high,
                estimatedImpact: 0.9
            ))
        } else if analysis.currentLoad > 0.6 {
            recommendations.append(CognitiveRecommendation(
                type: .taskSimplification,
                title: "Simplify Current Tasks",
                description: "Consider breaking down complex tasks into smaller, manageable pieces.",
                priority: .medium,
                estimatedImpact: 0.7
            ))
        }
        
        if analysis.sustainabilityScore < 0.5 {
            recommendations.append(CognitiveRecommendation(
                type: .pacing,
                title: "Adjust Your Pace",
                description: "Your current pace may not be sustainable. Consider slowing down.",
                priority: .medium,
                estimatedImpact: 0.8
            ))
        }
        
        return recommendations
    }
    
    func generateAttentionRecommendations(from analysis: AttentionAnalysis) -> [CognitiveRecommendation] {
        var recommendations: [CognitiveRecommendation] = []
        
        if analysis.averageFocusDuration < 900 { // Less than 15 minutes
            recommendations.append(CognitiveRecommendation(
                type: .focusImprovement,
                title: "Improve Focus Duration",
                description: "Try techniques like the Pomodoro method to extend focus periods.",
                priority: .medium,
                estimatedImpact: 0.7
            ))
        }
        
        if analysis.distractionFrequency > 5 {
            recommendations.append(CognitiveRecommendation(
                type: .distractionReduction,
                title: "Reduce Distractions",
                description: "Consider creating a more focused work environment.",
                priority: .high,
                estimatedImpact: 0.8
            ))
        }
        
        return recommendations
    }
    
    // MARK: - Utility Helpers
    
    private func calculatePatternConfidence(_ metrics: [AttentionMetrics]) -> Double {
        guard metrics.count > 1 else { return 0.0 }
        
        let values = metrics.map { $0.focusDuration }
        let mean = values.reduce(0, +) / Double(values.count)
        let variance = values.map { pow($0 - mean, 2) }.reduce(0, +) / Double(values.count)
        let standardDeviation = sqrt(variance)
        
        // Lower standard deviation = higher confidence
        return max(0.0, 1.0 - (standardDeviation / mean))
    }
    
    private func calculateTrend(_ values: [Double]) -> Double {
        guard values.count > 1 else { return 0.0 }
        
        let n = Double(values.count)
        let sumX = (1...values.count).reduce(0, +)
        let sumY = values.reduce(0, +)
        let sumXY = zip(1...values.count, values).map { Double($0.0) * $0.1 }.reduce(0, +)
        let sumX2 = (1...values.count).map { Double($0 * $0) }.reduce(0, +)
        
        let slope = (n * sumXY - Double(sumX) * sumY) / (n * sumX2 - Double(sumX * sumX))
        return slope
    }
    
    func normalizeScore(_ score: Double, min: Double = 0.0, max: Double = 1.0) -> Double {
        return Swift.max(min, Swift.min(max, score))
    }
    
    func weightedAverage(_ values: [(value: Double, weight: Double)]) -> Double {
        let totalWeight = values.map { $0.weight }.reduce(0, +)
        guard totalWeight > 0 else { return 0.0 }
        
        let weightedSum = values.map { $0.value * $0.weight }.reduce(0, +)
        return weightedSum / totalWeight
    }
}
