import Combine
import Foundation
import HealthKit

// MARK: - Required Type Definitions

@available(iOS 18.0, *)
public enum BehaviorTimeframe: String, Codable, CaseIterable, Sendable {
    case daily
    case weekly
    case monthly
    case quarterly
    case yearly
}

@available(iOS 18.0, *)
public enum BreathingExerciseType: String, Codable, CaseIterable, Sendable {
    case boxBreathing
    case deepBreathing
    case fourSevenEight
    case alternateNostril
    case bellowsBreath
    case coherentBreathing
    case triangleBreathing
    case customPattern
}

@available(iOS 18.0, *)
public enum ExerciseDifficulty: String, Codable, CaseIterable, Sendable {
    case beginner
    case intermediate
    case advanced
    case expert
}

@available(iOS 18.0, *)
public struct BreathingExercise: Codable, Identifiable, Equatable, Sendable {
    public let id: UUID
    public var name: String
    public var description: String
    public var type: BreathingExerciseType
    public var duration: TimeInterval
    public var inhalePattern: BreathingPattern
    public var exhalePattern: BreathingPattern
    public var holdPattern: BreathingPattern?
    public var difficulty: ExerciseDifficulty
    public var benefits: [String]
    public var instructions: [String]
    public var isCustom: Bool
    public var createdBy: UUID?
    public var usageCount: Int
    public var averageRating: Double
    public var tags: [String]
    public var neurodiversitySupport: [NeurodiversityType]
    public var cycles: Int // Claude: Adding missing cycles property

    public init(
        name: String,
        description: String,
        type: BreathingExerciseType,
        duration: TimeInterval,
        inhalePattern: BreathingPattern,
        exhalePattern: BreathingPattern,
        id: UUID = UUID(),
        holdPattern: BreathingPattern? = nil,
        difficulty: ExerciseDifficulty = .beginner,
        benefits: [String] = [],
        instructions: [String] = [],
        isCustom: Bool = false,
        createdBy: UUID? = nil,
        usageCount: Int = 0,
        averageRating: Double = 0.0,
        tags: [String] = [],
        neurodiversitySupport: [NeurodiversityType] = [],
        cycles: Int = 8 // Claude: Adding cycles parameter
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.type = type
        self.duration = duration
        self.inhalePattern = inhalePattern
        self.exhalePattern = exhalePattern
        self.holdPattern = holdPattern
        self.difficulty = difficulty
        self.benefits = benefits
        self.instructions = instructions
        self.isCustom = isCustom
        self.createdBy = createdBy
        self.usageCount = usageCount
        self.averageRating = averageRating
        self.tags = tags
        self.neurodiversitySupport = neurodiversitySupport
        self.cycles = cycles // Claude: Initialize cycles property
    }
    
    // Claude: Adding static defaultExercises property
    public static let defaultExercises: [BreathingExercise] = [
        BreathingExercise(
            name: "Box Breathing",
            description: "Equal counts for inhale, hold, exhale, and pause",
            type: .boxBreathing,
            duration: 480, // 8 minutes
            inhalePattern: BreathingPattern.boxBreathing,
            exhalePattern: BreathingPattern.boxBreathing,
            difficulty: .beginner,
            benefits: ["Reduces stress", "Improves focus"],
            instructions: ["Breathe in for 4 counts", "Hold for 4 counts", "Exhale for 4 counts", "Pause for 4 counts"],
            neurodiversitySupport: [.adhd, .anxiety],
            cycles: 8
        ),
        BreathingExercise(
            name: "Deep Breathing",
            description: "Longer exhale for relaxation",
            type: .deepBreathing,
            duration: 360, // 6 minutes
            inhalePattern: BreathingPattern.deepBreathing,
            exhalePattern: BreathingPattern.deepBreathing,
            difficulty: .beginner,
            benefits: ["Promotes relaxation", "Reduces anxiety"],
            instructions: ["Breathe in slowly for 4 counts", "Hold for 2 counts", "Exhale slowly for 6 counts", "Pause for 2 counts"],
            neurodiversitySupport: [.anxiety, .autism],
            cycles: 6
        )
    ]
}

// MARK: - Breathing Service Protocol

@available(iOS 18.0, *)
public protocol BreathingServiceProtocol {
    func startBreathingSession(_ exercise: BreathingExercise) async throws
    func pauseBreathingSession() async throws
    func resumeBreathingSession() async throws
    func endBreathingSession() async throws -> BreathingSessionResult
    func getAvailableExercises() async -> [BreathingExercise]
    func getPersonalizedExercises(for userProfile: UserProfile) async -> [BreathingExercise]
    func trackBreathingMetrics() async throws
    func getBreathingHistory(for timeframe: BehaviorTimeframe) async throws -> [BreathingSessionResult]
}

// MARK: - Breathing Service Implementation

@available(iOS 18.0, *)
@MainActor
public class BreathingService: BreathingServiceProtocol, ObservableObject {
    // MARK: - Properties

    @Published var currentSession: BreathingSession?
    @Published var isSessionActive: Bool = false
    @Published var currentPhase: BreathingPhase = .preparation
    @Published var currentCycle: Int = 0
    @Published var sessionProgress: Double = 0.0

    private let healthKitService: HealthKitServiceProtocol
    private let userService: UserServiceProtocol
    private let coreDataService: CoreDataServiceProtocol

    private var sessionTimer: Timer?
    private var phaseTimer: Timer?
    private var breathingMetrics: [BreathingMetric] = []

    // MARK: - Initialization

    init(
        healthKitService: HealthKitServiceProtocol,
        userService: UserServiceProtocol,
        coreDataService: CoreDataServiceProtocol
    ) {
        self.healthKitService = healthKitService
        self.userService = userService
        self.coreDataService = coreDataService
    }

    // MARK: - Session Management

    public func startBreathingSession(_ exercise: BreathingExercise) async throws {
        guard !isSessionActive else {
            throw BreathingServiceError.sessionAlreadyActive
        }

        // Create new session
        let session = BreathingSession(
            exerciseId: exercise.id,
            targetCycles: 8, // Default cycles since BreathingExercise doesn't have cycles
            pattern: exercise.inhalePattern, // Use inhalePattern instead of pattern
            id: UUID(),
            startTime: Date(),
            userProfile: try await userService.getCurrentUser()
        )

        currentSession = session
        isSessionActive = true
        currentPhase = .preparation
        currentCycle = 0
        sessionProgress = 0.0

        // Start health tracking
        try await healthKitService.startBreathingSession(session)

        // Begin breathing sequence
        await startBreathingSequence(exercise)
    }

    public func pauseBreathingSession() async throws {
        guard isSessionActive, let session = currentSession else {
            throw BreathingServiceError.noActiveSession
        }

        sessionTimer?.invalidate()
        phaseTimer?.invalidate()

        // Update session state
        var updatedSession = session
        updatedSession.isPaused = true
        updatedSession.pausedAt = Date()
        currentSession = updatedSession

        // Pause health tracking
        try await healthKitService.pauseBreathingSession()
    }

    public func resumeBreathingSession() async throws {
        guard isSessionActive, let session = currentSession, session.isPaused else {
            throw BreathingServiceError.sessionNotPaused
        }

        // Update session state
        var updatedSession = session
        updatedSession.isPaused = false
        updatedSession.pausedAt = nil
        currentSession = updatedSession

        // Resume health tracking
        try await healthKitService.resumeBreathingSession()

        // Resume breathing sequence
        await resumeBreathingSequence()
    }

    public func endBreathingSession() async throws -> BreathingSessionResult {
        guard let session = currentSession else {
            throw BreathingServiceError.noActiveSession
        }

        sessionTimer?.invalidate()
        phaseTimer?.invalidate()

        // End health tracking
        let healthData = try await healthKitService.endBreathingSession()

        // Calculate session results
        let result = await calculateSessionResult(session: session, healthData: healthData)

        // Save session data
        try await saveSessionResult(result)

        // Reset state
        currentSession = nil
        isSessionActive = false
        currentPhase = .complete
        breathingMetrics.removeAll()

        return result
    }

    // MARK: - Exercise Management (moved to BreathingServiceHelpers.swift)

    public func getPersonalizedExercises(for userProfile: UserProfile) async -> [BreathingExercise] {
        let allExercises = await getAvailableExercises()

        // Filter exercises based on user's neurodiversity profile
        let personalizedExercises = allExercises.filter { exercise in
            let userTypes = userProfile.neurodiversityProfile.types
            return exercise.neurodiversitySupport.contains { userTypes.contains($0) }
        }

        // Sort by difficulty preference
        let sortedExercises = personalizedExercises.sorted { exercise1, exercise2 in
            let userDifficulty = userProfile.preferences.preferredDifficulty ?? .beginner
            return exercise1.difficulty == userDifficulty && exercise2.difficulty != userDifficulty
        }

        return sortedExercises.isEmpty ? allExercises : sortedExercises
    }

    // MARK: - Metrics and History

    public func trackBreathingMetrics() async throws {
        guard isSessionActive else { return }

        // Get current health metrics
        let heartRate = try await healthKitService.getCurrentHeartRate()
        let hrvReading = try await healthKitService.getCurrentHRV()

        let metric = BreathingMetric(
            timestamp: Date(),
            heartRate: heartRate,
            hrv: hrvReading,
            phase: currentPhase,
            cycle: currentCycle
        )

        breathingMetrics.append(metric)
    }

    // MARK: - History retrieval moved to BreathingServiceHelpers.swift

    // MARK: - Private Methods

    private func startBreathingSequence(_ exercise: BreathingExercise) async {
        await executeBreathingCycles(exercise)
    }

    private func resumeBreathingSequence() async {
        guard let session = currentSession else { return }

        // Continue from current cycle and phase
        await continueBreathingFromCurrentState(session)
    }

    private func executeBreathingCycles(_ exercise: BreathingExercise) async {
        for cycle in currentCycle..<exercise.cycles {
            guard isSessionActive, currentSession?.isPaused == false else { break }

            currentCycle = cycle + 1
            await executeBreathingPhases(exercise.inhalePattern) // Claude: Using inhalePattern instead of pattern

            // Update progress
            sessionProgress = Double(currentCycle) / Double(exercise.cycles)

            // Track metrics
            try? await trackBreathingMetrics()
        }

        if currentCycle >= exercise.cycles {
            currentPhase = .complete
        }
    }

    private func executeBreathingPhases(_ pattern: BreathingPattern) async {
        let phases: [(BreathingPhase, Int)] = [
            (.inhale, pattern.inhaleCount),
            (.hold, pattern.holdCount),
            (.exhale, pattern.exhaleCount),
            (.pause, pattern.pauseCount)
        ]

        for (phase, duration) in phases {
            guard isSessionActive, currentSession?.isPaused == false else { break }

            currentPhase = phase
            await waitForPhase(duration: duration)
        }
    }

    // MARK: - Continue breathing moved to BreathingServiceHelpers.swift

    // MARK: - Phase timing moved to BreathingServiceHelpers.swift

    private func calculateSessionResult(session: BreathingSession, healthData: HealthKitBreathingData) async -> BreathingSessionResult {
        let endTime = Date()
        let duration = endTime.timeIntervalSince(session.startTime)

        // Calculate metrics
        let averageHeartRate = healthData.heartRateReadings.map { $0.value }.reduce(0, +) / Double(healthData.heartRateReadings.count)
        let stressReduction = calculateStressReduction(healthData)
        let anxietyImprovement = calculateAnxietyImprovement(healthData)

        return BreathingSessionResult(
            sessionId: session.id,
            startTime: session.startTime,
            endTime: endTime,
            pattern: session.pattern,
            completedCycles: currentCycle,
            targetCycles: session.targetCycles,
            averageHeartRate: averageHeartRate,
            hrvData: healthData.hrvReadings,
            stressReduction: stressReduction,
            anxietyImprovement: anxietyImprovement
        )
    }

    private func calculateStressReduction(_ healthData: HealthKitBreathingData) -> Double {
        // Calculate stress reduction based on HRV improvement
        guard !healthData.hrvReadings.isEmpty else { return 0.0 }

        let initialHRV = healthData.hrvReadings.prefix(3).map { $0.value }.reduce(0, +) / 3
        let finalHRV = healthData.hrvReadings.suffix(3).map { $0.value }.reduce(0, +) / 3

        return max(0, (finalHRV - initialHRV) / initialHRV)
    }

    private func calculateAnxietyImprovement(_ healthData: HealthKitBreathingData) -> Double {
        // Calculate anxiety improvement based on heart rate variability
        guard !healthData.heartRateReadings.isEmpty else { return 0.0 }

        let heartRates = healthData.heartRateReadings.map { $0.value }
        let initialVariability = calculateVariability(heartRates.prefix(5))
        let finalVariability = calculateVariability(heartRates.suffix(5))

        return max(0, (finalVariability - initialVariability) / max(initialVariability, 1.0))
    }

    private func calculateVariability(_ values: any Sequence<Double>) -> Double {
        let array = Array(values)
        guard array.count > 1 else { return 0.0 }

        let mean = array.reduce(0, +) / Double(array.count)
        let variance = array.map { pow($0 - mean, 2) }.reduce(0, +) / Double(array.count)
        return sqrt(variance)
    }

    // MARK: - Save method moved to BreathingServiceHelpers.swift
}

// MARK: - Supporting Models

@available(iOS 18.0, *)
struct BreathingMetric: Identifiable, Codable {
    let id = UUID()
    let timestamp: Date
    let heartRate: Double?
    let hrv: HRVReading?
    let phase: BreathingPhase
    let cycle: Int

    init(timestamp: Date, heartRate: Double?, hrv: HRVReading?, phase: BreathingPhase, cycle: Int) {
        self.timestamp = timestamp
        self.heartRate = heartRate
        self.hrv = hrv
        self.phase = phase
        self.cycle = cycle
    }
}

@available(iOS 18.0, *)
struct HealthKitBreathingData: Codable {
    let heartRateReadings: [HeartRateReading]
    let hrvReadings: [HRVReading]
    let respiratoryRate: Double?
    let oxygenSaturation: Double?

    init(
        heartRateReadings: [HeartRateReading] = [],
        hrvReadings: [HRVReading] = [],
        respiratoryRate: Double? = nil,
        oxygenSaturation: Double? = nil
    ) {
        self.heartRateReadings = heartRateReadings
        self.hrvReadings = hrvReadings
        self.respiratoryRate = respiratoryRate
        self.oxygenSaturation = oxygenSaturation
    }
}

@available(iOS 18.0, *)
public struct HeartRateReading: Identifiable, Codable, Sendable {
    public let id = UUID()
    public let timestamp: Date
    public let value: Double
    public let unit: String

    public init(value: Double, timestamp: Date = Date(), unit: String = "bpm") {
        self.timestamp = timestamp
        self.value = value
        self.unit = unit
    }
}

// MARK: - Errors

@available(iOS 18.0, *)
public enum BreathingServiceError: Error, LocalizedError {
    case noActiveSession
    case sessionAlreadyActive
    case sessionNotPaused
    case invalidSessionData
    case saveFailed
    case healthKitNotAvailable
    case watchNotConnected

    public var errorDescription: String? {
        switch self {
        case .noActiveSession:
            return "No active breathing session"
        case .sessionAlreadyActive:
            return "A breathing session is already active"
        case .sessionNotPaused:
            return "Session is not currently paused"
        case .invalidSessionData:
            return "Invalid session data provided"
        case .saveFailed:
            return "Failed to save breathing session"
        case .healthKitNotAvailable:
            return "HealthKit is not available"
        case .watchNotConnected:
            return "Apple Watch is not connected"
        }
    }
}