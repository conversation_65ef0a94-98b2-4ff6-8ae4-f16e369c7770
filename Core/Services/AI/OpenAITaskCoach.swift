import Combine
import Foundation

// MARK: - OpenAI Intelligence Service Protocol
// Replaces Apple AI with OpenAI for all AI-powered features

@available(iOS 18.0, *)
protocol OpenAIIntelligenceServiceProtocol {
    func generatePersonalizedContent(for context: UserContext) async throws -> PersonalizedContent
    func analyzeUserBehavior(_ data: UserBehaviorData) async throws -> BehaviorInsights
    func predictUserNeeds(_ context: UserContext) async throws -> [UserNeed]
    func adaptUIForCognitiveLoad(_ level: CognitiveLoadLevel) -> CognitiveAdaptation
    func suggestCognitiveBreak() async -> BreakSuggestion?
    func isAvailable() async -> Bool
}

// MARK: - OpenAI Task Coach Integration for NeuroNexa

/// OpenAI-powered task coaching service with neurodiversity-first approach
/// Replaces Apple AI with OpenAI for personalized task generation and cognitive support
@available(iOS 18.0, *)
class OpenAITaskCoach: ObservableObject, OpenAIIntelligenceServiceProtocol {
    private let openAIService = OpenAIService()
    private var cancellables = Set<AnyCancellable>()
    private let userDefaults = UserDefaults.standard

    @Published var isInitialized = false
    @Published var currentInsights: [CognitiveInsight] = []
    @Published var isProcessing = false

    func initialize() async {
        do {
            try await openAIService.initialize()

            await MainActor.run {
                isInitialized = true
            }
        } catch {
            print("Failed to initialize OpenAI service: \(error)")
        }
    }

    // MARK: - OpenAI Intelligence Service Protocol Implementation

    func generatePersonalizedContent(for context: UserContext) async throws -> PersonalizedContent {
        guard await isAvailable() else {
            throw OpenAIIntelligenceError.serviceUnavailable
        }

        let prompt = createPersonalizedContentPrompt(for: context)

        do {
            let response = try await openAIService.generateCompletion(prompt: prompt)
            let content = try parsePersonalizedContent(from: response, context: context)
            return content
        } catch {
            throw OpenAIIntelligenceError.contentGenerationFailure
        }
    }

    func analyzeUserBehavior(_ data: UserBehaviorData) async throws -> BehaviorInsights {
        guard await isAvailable() else {
            throw OpenAIIntelligenceError.serviceUnavailable
        }

        let prompt = createBehaviorAnalysisPrompt(for: data)

        do {
            let response = try await openAIService.generateCompletion(prompt: prompt)
            let insights = try parseBehaviorInsights(from: response, data: data)
            return insights
        } catch {
            throw OpenAIIntelligenceError.analysisFailure
        }
    }

    func predictUserNeeds(_ context: UserContext) async throws -> [UserNeed] {
        guard await isAvailable() else {
            throw OpenAIIntelligenceError.serviceUnavailable
        }

        let prompt = createUserNeedsPredictionPrompt(for: context)

        do {
            let response = try await openAIService.generateCompletion(prompt: prompt)
            let needs = try parseUserNeeds(from: response, context: context)
            return needs
        } catch {
            throw OpenAIIntelligenceError.predictionFailure
        }
    }

    func adaptUIForCognitiveLoad(_ level: CognitiveLoadLevel) -> CognitiveAdaptation {
        // Enhanced UI adaptation using neurodiversity-first principles
        switch level {
        case .low:
            return CognitiveAdaptation(
                id: UUID(),
                cognitiveLoadLevel: level,
                uiSimplification: .minimal,
                colorScheme: .standard,
                animationReduction: .none,
                textSize: .standard,
                spacing: .standard,
                interactionComplexity: .full,
                createdAt: Date()
            )

        case .medium:
            return CognitiveAdaptation(
                id: UUID(),
                cognitiveLoadLevel: level,
                uiSimplification: .moderate,
                colorScheme: .highContrast,
                animationReduction: .reduced,
                textSize: .large,
                spacing: .increased,
                interactionComplexity: .simplified,
                createdAt: Date()
            )

        case .high:
            return CognitiveAdaptation(
                id: UUID(),
                cognitiveLoadLevel: level,
                uiSimplification: .maximum,
                colorScheme: .minimal,
                animationReduction: .disabled,
                textSize: .extraLarge,
                spacing: .maximum,
                interactionComplexity: .minimal,
                createdAt: Date()
            )
        }
    }

    func suggestCognitiveBreak() async -> BreakSuggestion? {
        let currentTime = Date()
        let lastBreakTime = userDefaults.object(forKey: "last_break_time") as? Date ?? Date.distantPast
        let timeSinceLastBreak = currentTime.timeIntervalSince(lastBreakTime)

        // Enhanced break suggestion using OpenAI for personalization
        if timeSinceLastBreak > 2_700 { // 45 minutes
            do {
                let prompt = createBreakSuggestionPrompt(timeSinceLastBreak: timeSinceLastBreak)
                let response = try await openAIService.generateCompletion(prompt: prompt)
                let suggestion = try parseBreakSuggestion(from: response)
                return suggestion
            } catch {
                // Fallback to basic suggestion
                return BreakSuggestion(
                    id: UUID(),
                    type: .cognitive,
                    duration: 300,
                    title: "Time for a Cognitive Break",
                    description: "You've been focused for a while. A short break can help refresh your mind.",
                    activities: [
                        "Take deep breaths",
                        "Look away from the screen",
                        "Do light stretching",
                        "Practice mindfulness"
                    ],
                    urgency: .medium,
                    createdAt: currentTime
                )
            }
        }

        return nil
    }

    func isAvailable() async -> Bool {
        isInitialized && openAIService.isConfigured
    }

    func generateTaskBreakdown(for task: Task, userProfile: NeurodiversityProfile) async -> TaskBreakdown {
        guard isInitialized else {
            return TaskBreakdown.fallback(for: task)
        }

        let prompt = createTaskBreakdownPrompt(task: task, profile: userProfile)

        do {
            await MainActor.run {
                isProcessing = true
            }

            let response = try await openAIService.generateCompletion(prompt: prompt)
            let breakdown = try parseTaskBreakdown(from: response, originalTask: task)

            await MainActor.run {
                isProcessing = false
            }

            return breakdown
        } catch {
            print("Task breakdown generation failed: \(error)")

            await MainActor.run {
                isProcessing = false
            }

            return TaskBreakdown.fallback(for: task)
        }
    }

    func provideCognitiveSupport(for user: User) async -> [CognitiveInsight] {
        guard isInitialized else { return [] }

        let prompt = createCognitiveSupportPrompt(user: user)

        do {
            let response = try await openAIService.generateCompletion(prompt: prompt)
            let insights = try parseCognitiveInsights(from: response)

            await MainActor.run {
                currentInsights = insights
            }

            return insights
        } catch {
            print("Cognitive support generation failed: \(error)")
            return []
        }
    }

    func optimizeRoutine(_ routine: Routine, for profile: NeurodiversityProfile) async -> OptimizedRoutine {
        guard isInitialized else {
            return OptimizedRoutine.fallback(from: routine)
        }

        let prompt = createRoutineOptimizationPrompt(routine: routine, profile: profile)

        do {
            let response = try await openAIService.generateCompletion(prompt: prompt)
            return try parseOptimizedRoutine(from: response, originalRoutine: routine)
        } catch {
            print("Routine optimization failed: \(error)")
            return OptimizedRoutine.fallback(from: routine)
        }
    }

    func generatePersonalizedRecommendations(for user: User) async -> [PersonalizedRecommendation] {
        guard isInitialized else { return [] }

        let prompt = createRecommendationsPrompt(user: user)

        do {
            let response = try await openAIService.generateCompletion(prompt: prompt)
            return try parsePersonalizedRecommendations(from: response)
        } catch {
            print("Recommendation generation failed: \(error)")
            return []
        }
    }

    // MARK: - Private Methods

    private func createTaskBreakdownPrompt(task: Task, profile: NeurodiversityProfile) -> String {
        """
        You are an expert neurodiversity coach specializing in ADHD and autism support.
        Break down this task for someone with the following profile:

        Task: \(task.title)
        Description: \(task.description ?? "No description provided")
        Estimated Duration: \(task.estimatedDuration) minutes
        Priority: \(task.priority.rawValue)

        User Profile:
        - Neurodiversity Type: \(profile.primaryType.rawValue)
        - Executive Function Level: \(profile.executiveFunctionLevel.rawValue)
        - Sensory Sensitivity: \(profile.sensoryPreferences.motionSensitivity.rawValue)
        - Cognitive Load Preference: \(profile.cognitiveLoadPreference.rawValue)

        Please provide:
        1. 3-5 specific, actionable subtasks
        2. Estimated time for each subtask (5-25 minutes max)
        3. Clear success criteria for each step
        4. Potential challenges and coping strategies
        5. Sensory considerations and accommodations

        Format as JSON with this structure:
        {
          "subtasks": [
            {
              "title": "Step title",
              "description": "Detailed description",
              "estimatedMinutes": 15,
              "successCriteria": "What success looks like",
              "challenges": ["Potential challenge 1", "Challenge 2"],
              "accommodations": ["Accommodation 1", "Accommodation 2"]
            }
          ],
          "overallStrategy": "High-level approach",
          "cognitiveLoadTips": ["Tip 1", "Tip 2"]
        }
        """
    }

    private func createCognitiveSupportPrompt(user: User) -> String {
        """
        Provide cognitive support insights for a neurodivergent user based on their current state:

        Current Tasks: \(user.activeTasks.count) active tasks
        Energy Level: \(user.currentEnergyLevel.rawValue)
        Focus State: \(user.currentFocusState.rawValue)
        Time of Day: \(DateFormatter.localizedString(from: Date(), dateStyle: .none, timeStyle: .short))

        Neurodiversity Profile:
        - Type: \(user.neurodiversityProfile.primaryType.rawValue)
        - Executive Function: \(user.neurodiversityProfile.executiveFunctionLevel.rawValue)

        Provide insights as JSON:
        {
          "insights": [
            {
              "type": "energy_management|focus_optimization|task_prioritization|sensory_regulation",
              "title": "Insight title",
              "description": "Detailed insight",
              "actionable_steps": ["Step 1", "Step 2"],
              "priority": "high|medium|low"
            }
          ]
        }
        """
    }

    private func createRoutineOptimizationPrompt(routine: Routine, profile: NeurodiversityProfile) -> String {
        """
        Optimize this routine for a neurodivergent individual:

        Routine: \(routine.name)
        Current Steps: \(routine.steps.count)
        Total Duration: \(routine.estimatedDuration) minutes

        User Profile:
        - Neurodiversity: \(profile.primaryType.rawValue)
        - Executive Function: \(profile.executiveFunctionLevel.rawValue)
        - Sensory Needs: \(profile.sensoryPreferences.description)

        Provide optimized routine as JSON with improved timing, order, and accommodations.
        """
    }

    private func createRecommendationsPrompt(user: User) -> String {
        """
        Generate personalized recommendations for this neurodivergent user:

        Recent Activity: \(user.recentActivity.description)
        Current Challenges: \(user.currentChallenges.map { $0.description }.joined(separator: ", "))
        Preferences: \(user.preferences.description)

        Provide 3-5 actionable recommendations as JSON.
        """
    }

    // MARK: - Response Parsing

    private func parseTaskBreakdown(from response: String, originalTask: Task) throws -> TaskBreakdown {
        // Parse JSON response and create TaskBreakdown object
        guard let data = response.data(using: .utf8),
              let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let subtasksArray = json["subtasks"] as? [[String: Any]] else {
            throw OpenAIError.invalidResponse
        }

        let subtasks = try subtasksArray.compactMap { subtaskDict -> TaskBreakdownStep? in
            guard let title = subtaskDict["title"] as? String,
                  let description = subtaskDict["description"] as? String,
                  let estimatedMinutes = subtaskDict["estimatedMinutes"] as? Int else {
                return nil
            }

            return TaskBreakdownStep(
                title: title,
                description: description,
                estimatedDuration: estimatedMinutes,
                successCriteria: subtaskDict["successCriteria"] as? String ?? "",
                challenges: subtaskDict["challenges"] as? [String] ?? [],
                accommodations: subtaskDict["accommodations"] as? [String] ?? []
            )
        }

        return TaskBreakdown(
            originalTask: originalTask,
            steps: subtasks,
            strategy: json["overallStrategy"] as? String ?? "",
            cognitiveLoadTips: json["cognitiveLoadTips"] as? [String] ?? []
        )
    }

    private func parseCognitiveInsights(from response: String) throws -> [CognitiveInsight] {
        guard let data = response.data(using: .utf8),
              let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let insightsArray = json["insights"] as? [[String: Any]] else {
            throw OpenAIError.invalidResponse
        }

        return try insightsArray.compactMap { insightDict -> CognitiveInsight? in
            guard let type = insightDict["type"] as? String,
                  let title = insightDict["title"] as? String,
                  let description = insightDict["description"] as? String else {
                return nil
            }

            return CognitiveInsight(
                type: CognitiveInsightType(rawValue: type) ?? .general,
                title: title,
                description: description,
                actionableSteps: insightDict["actionable_steps"] as? [String] ?? [],
                priority: CognitiveInsightPriority(rawValue: insightDict["priority"] as? String ?? "medium") ?? .medium
            )
        }
    }

    private func parseOptimizedRoutine(from response: String, originalRoutine: Routine) throws -> OptimizedRoutine {
        // Implementation for parsing optimized routine
        OptimizedRoutine.fallback(from: originalRoutine)
    }

    private func parsePersonalizedRecommendations(from response: String) throws -> [PersonalizedRecommendation] {
        // Implementation for parsing recommendations
        []
    }
}

// MARK: - OpenAI Service

class OpenAIService {
    private let apiKey: String
    private let baseURL = "https://api.openai.com/v1"

    init() {
        self.apiKey = AppConfiguration.openAIAPIKey
    }

    func initialize() async throws {
        // Validate API key and connection
        _ = try await generateCompletion(prompt: "Test connection", maxTokens: 10)
    }

    func generateCompletion(prompt: String, maxTokens: Int = 1_000) async throws -> String {
        guard let url = URL(string: "\(baseURL)/chat/completions") else {
            throw OpenAIError.invalidURL
        }
        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("Bearer \(apiKey)", forHTTPHeaderField: "Authorization")
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody: [String: Any] = [
            "model": "gpt-4",
            "messages": [
                ["role": "user", "content": prompt]
            ],
            "max_tokens": maxTokens,
            "temperature": 0.7
        ]

        request.httpBody = try JSONSerialization.data(withJSONObject: requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse,
              httpResponse.statusCode == 200 else {
            throw OpenAIError.networkError
        }

        guard let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
              let choices = json["choices"] as? [[String: Any]],
              let firstChoice = choices.first,
              let message = firstChoice["message"] as? [String: Any],
              let content = message["content"] as? String else {
            throw OpenAIError.invalidResponse
        }

        return content
    }

    // MARK: - OpenAI Prompt Generation Methods
    // Moved to OpenAITaskCoachParsing.swift

    // MARK: - Prompt Generation Methods
    // Moved to OpenAITaskCoachParsing.swift

    // MARK: - OpenAI Response Parsing Methods
    // Moved to OpenAITaskCoachParsing.swift
}