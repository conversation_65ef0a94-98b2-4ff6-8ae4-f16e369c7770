import SwiftUI

// MARK: - Cognitive Progress View Style

/// A custom progress view style that adapts to cognitive load and sensory preferences
@available(iOS 18.0, *)
struct CognitiveProgressViewStyle: ProgressViewStyle {
    let cognitiveLoad: CognitiveLoadLevel
    let sensoryPreferences: SensoryPreferences
    
    @State private var animationProgress: Double = 0

    func makeBody(configuration: Configuration) -> some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // Background track
                RoundedRectangle(cornerRadius: trackHeight / 2)
                    .fill(Color.neuroNexaTextSecondary.opacity(0.2))
                    .frame(height: trackHeight)

                // Progress fill with gradient
                RoundedRectangle(cornerRadius: trackHeight / 2)
                    .fill(progressGradient)
                    .frame(
                        width: geometry.size.width * CGFloat(animationProgress),
                        height: trackHeight
                    )
                    .animation(
                        .easeInOut(duration: animationDuration),
                        value: animationProgress
                    )
                    .onAppear {
                        animationProgress = configuration.fractionCompleted ?? 0
                    }
                    .onChange(of: configuration.fractionCompleted) { _, newValue in
                        withAnimation(.easeInOut(duration: animationDuration)) {
                            animationProgress = newValue ?? 0
                        }
                    }
                
                // Accessibility overlay
                Rectangle()
                    .fill(Color.clear)
                    .frame(width: geometry.size.width, height: trackHeight)
                    .accessibilityElement(children: .ignore)
                    .accessibilityLabel("Progress indicator")
                    .accessibilityValue("\\(Int((configuration.fractionCompleted ?? 0) * 100)) percent complete")
                    .accessibilityAddTraits(.updatesFrequently)
            }
        }
        .frame(height: trackHeight)
        .accessibilityElement(children: .contain)
    }

    private var trackHeight: CGFloat {
        switch cognitiveLoad {
        case .low: return 6
        case .medium: return 8
        case .high: return 10
        case .overload: return 12
        }
    }

    private var progressGradient: LinearGradient {
        LinearGradient(
            gradient: Gradient(colors: [
                Color.neuroNexaPrimary.opacity(0.8),
                Color.neuroNexaPrimary
            ]),
            startPoint: .leading,
            endPoint: .trailing
        )
    }
    
    private var animationDuration: Double {
        switch sensoryPreferences.motionSensitivity {
        case 0.0..<0.3: return 0.1  // Minimal motion
        case 0.3..<0.6: return 0.3  // Reduced motion
        case 0.6..<0.8: return 0.5  // Standard motion
        default: return 0.8         // Full motion
        }
    }
}

// MARK: - Convenience Extensions

@available(iOS 18.0, *)
extension ProgressView {
    func cognitiveStyle(
        cognitiveLoad: CognitiveLoadLevel = .medium,
        sensoryPreferences: SensoryPreferences = .default
    ) -> some View {
        self.progressViewStyle(
            CognitiveProgressViewStyle(
                cognitiveLoad: cognitiveLoad,
                sensoryPreferences: sensoryPreferences
            )
        )
    }
}

// MARK: - Supporting Types

extension SensoryPreferences {
    static let `default` = SensoryPreferences(
        motionSensitivity: 0.5,
        vibrationIntensity: VibrationIntensity.medium
    )
}

enum VibrationIntensity: Double, CaseIterable {
    case off = 0.0
    case light = 0.3
    case medium = 0.5
    case strong = 0.8
    
    var intensity: Double {
        return self.rawValue
    }
}

struct SensoryPreferences {
    let motionSensitivity: Double
    let vibrationIntensity: VibrationIntensity
}

enum CognitiveLoadLevel: String, CaseIterable {
    case low = "Low"
    case medium = "Medium"
    case high = "High"
    case overload = "Overload"
}
