import SwiftUI

// MARK: - Cognitive Progress View Style

/// A custom progress view style that adapts to cognitive load and sensory preferences
@available(iOS 26.0, *)
struct CognitiveProgressViewStyle: ProgressViewStyle {
    let cognitiveLoad: CognitiveLoadLevel
    let sensoryPreferences: SensoryPreferences

    func makeBody(configuration: Configuration) -> some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // Background track
                RoundedRectangle(cornerRadius: trackHeight / 2)
                    .fill(Color.secondary.opacity(0.2))
                    .frame(height: trackHeight)

                // Progress fill
                RoundedRectangle(cornerRadius: trackHeight / 2)
                    .fill(progressColor)
                    .frame(
                        width: geometry.size.width * CGFloat(configuration.fractionCompleted ?? 0),
                        height: trackHeight
                    )
                    .animation(
                        NeuroNexaDesignSystem.Animation.adaptive(for: sensoryPreferences.motionSensitivity),
                        value: configuration.fractionCompleted
                    )
            }
        }
        .frame(height: trackHeight)
    }

    private var trackHeight: CGFloat {
        switch cognitiveLoad {
        case .low: return 4
        case .medium: return 6
        case .high: return 8
        case .overload: return 10
        }
    }

    private var progressColor: Color {
        NeuroNexaDesignSystem.Colors.primaryBlue
    }
}
