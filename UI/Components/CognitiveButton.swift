import SwiftUI

// MARK: - Cognitive Button - Neurodiversity-Optimized Button Component

/// A button component specifically designed for neurodiversity support
/// Features adaptive sizing, cognitive load awareness, and sensory considerations
@available(iOS 18.0, *)
struct CognitiveButton: View {
    // MARK: - Properties
    let title: String
    let action: () -> Void
    let style: CognitiveButtonStyle
    let priority: TaskPriority

    // Environment values for adaptive behavior
    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPreferences
    @Environment(\.accessibilitySettings) private var accessibilitySettings
    @Environment(\.executiveFunctionLevel) private var executiveFunctionLevel

    // State for interaction feedback
    @State private var isPressed = false
    @State private var isHovered = false

    // MARK: - Initialization
    init(
        _ title: String,
        style: CognitiveButtonStyle = .primary,
        priority: TaskPriority = .medium,
        action: @escaping () -> Void
    ) {
        self.title = title
        self.style = style
        self.priority = priority
        self.action = action
    }

    // MARK: - Body
    var body: some View {
        let baseButton = buttonContent
            .buttonStyle(CognitiveButtonPressStyle())

        let accessibleButton = baseButton
            .accessibilityLabel(accessibilityLabel)
            .accessibilityHint(accessibilityHint)
            .accessibilityAddTraits(accessibilityTraits)
            .accessibilityIdentifier("CognitiveButton_\(title.replacingOccurrences(of: " ", with: "_"))")
            .accessibilityValue(accessibilityValue)
            .accessibilityRespondsToUserInteraction(true)
            .accessibilityShowsLargeContentViewer()
            .accessibilityInputLabels([accessibilityInputLabel])
            .dynamicTypeSize(.small ... .accessibility5)

        let interactiveButton = accessibleButton
            .onLongPressGesture(minimumDuration: 0.1, maximumDistance: 50) {
                // Provide haptic feedback for executive function support
                provideHapticFeedback()
                announceActivation()
                action()
            } onPressingChanged: { pressing in
                withAnimation(adaptiveAnimation) {
                    isPressed = pressing
                }
                // Announce press state for screen readers
                if pressing {
                    UIAccessibility.post(notification: .announcement, argument: "Button pressed")
                }
            }
            .sensoryFeedback(.impact(intensity: 0.5), trigger: isPressed)
            .sensoryFeedback(.success, trigger: action)
            .accessibilityAction(named: "Activate") {
                announceActivation()
                action()
            }
            .accessibilityAction(named: "Get more information") {
                announceDetailedInfo()
            }

        return interactiveButton
            .onHover { hovering in
                withAnimation(adaptiveAnimation) {
                    isHovered = hovering
                }
                // Announce hover state for assistive technologies
                if hovering {
                    UIAccessibility.post(
                        notification: .announcement,
                        argument: "\(title) button focused"
                    )
                }
            }
            .focusable(true)
            .focusEffectDisabled(false)
    }

    // MARK: - Button Content
    private var buttonContent: some View {
        Button(action: action) {
            buttonLabel
                .padding(adaptivePadding)
                .frame(minHeight: minimumTouchTarget)
                .background(backgroundColor)
                .cornerRadius(adaptiveCornerRadius)
                .neuroNexaShadow(adaptiveShadow)
                .scaleEffect(interactionScale)
                .animation(adaptiveAnimation, value: isPressed)
                .animation(adaptiveAnimation, value: isHovered)
        }
    }

    // MARK: - Button Label
    private var buttonLabel: some View {
        HStack(spacing: adaptiveSpacing) {
            // Icon based on priority (optional)
            if shouldShowIcon {
                priorityIcon
                    .font(adaptiveIconSize)
                    .foregroundColor(iconColor)
            }

            // Button text
            Text(title)
                .font(adaptiveFont)
                .fontWeight(adaptiveFontWeight)
                .foregroundColor(textColor)
                .lineLimit(nil)
                .multilineTextAlignment(.center)
                .fixedSize(horizontal: false, vertical: true)
        }
    }

    // MARK: - Computed Properties

    private var adaptiveFont: Font {
        let baseFont = switch style {
        case .primary: NeuroNexaDesignSystem.Typography.headline
        case .secondary: NeuroNexaDesignSystem.Typography.body
        case .tertiary: NeuroNexaDesignSystem.Typography.callout
        case .destructive: NeuroNexaDesignSystem.Typography.headline
        }

        return NeuroNexaDesignSystem.Typography.adaptiveFont(
            base: baseFont,
            cognitiveLoad: cognitiveLoad
        )
    }

    private var adaptiveFontWeight: Font.Weight {
        switch cognitiveLoad {
        case .low: .regular
        case .medium: .medium
        case .high: .semibold
        case .overload: .bold
        }
    }

    private var adaptivePadding: EdgeInsets {
        let basePadding = switch style {
        case .primary: EdgeInsets(top: 16, leading: 24, bottom: 16, trailing: 24)
        case .secondary: EdgeInsets(top: 12, leading: 20, bottom: 12, trailing: 20)
        case .tertiary: EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16)
        case .destructive: EdgeInsets(top: 16, leading: 24, bottom: 16, trailing: 24)
        }

        let multiplier = NeuroNexaDesignSystem.Spacing.adaptiveSpacing(
            base: 1.0,
            cognitiveLoad: cognitiveLoad
        )

        return EdgeInsets(
            top: basePadding.top * multiplier,
            leading: basePadding.leading * multiplier,
            bottom: basePadding.bottom * multiplier,
            trailing: basePadding.trailing * multiplier
        )
    }

    private var adaptiveSpacing: CGFloat {
        NeuroNexaDesignSystem.Spacing.adaptiveSpacing(
            base: NeuroNexaDesignSystem.Spacing.sm,
            cognitiveLoad: cognitiveLoad
        )
    }

    private var adaptiveCornerRadius: CGFloat {
        NeuroNexaDesignSystem.CornerRadius.adaptive(for: cognitiveLoad)
    }

    private var adaptiveShadow: Shadow {
        let baseShadow = switch style {
        case .primary: NeuroNexaDesignSystem.Shadows.medium
        case .secondary: NeuroNexaDesignSystem.Shadows.soft
        case .tertiary: NeuroNexaDesignSystem.Shadows.subtle
        case .destructive: NeuroNexaDesignSystem.Shadows.medium
        }

        // Apply sensory adaptations to the base shadow
        let adaptiveShadowSettings = NeuroNexaDesignSystem.Shadows.adaptiveShadow(for: sensoryPreferences)

        // Combine base shadow properties with adaptive adjustments
        return Shadow(
            color: baseShadow.color,
            radius: baseShadow.radius * (adaptiveShadowSettings.radius / 4.0), // Normalize radius scaling
            x: baseShadow.x,
            y: baseShadow.y
        )
    }

    private var backgroundColor: Color {
        let baseColor = switch style {
        case .primary: Color.neuroNexaPrimary
        case .secondary: Color.neuroNexaSecondary
        case .tertiary: Color.clear
        case .destructive: Color.red
        }

        return isPressed ? baseColor.opacity(0.8) :
            isHovered ? baseColor.opacity(0.9) :
            baseColor
    }

    private var textColor: Color {
        switch style {
        case .primary, .destructive: .white
        case .secondary: Color.neuroNexaText
        case .tertiary: Color.neuroNexaPrimary
        }
    }

    private var iconColor: Color {
        textColor
    }

    private var interactionScale: CGFloat {
        if isPressed {
            0.95
        } else if isHovered {
            1.02
        } else {
            1.0
        }
    }

    private var minimumTouchTarget: CGFloat {
        // Ensure minimum 44pt touch target for accessibility
        max(44, adaptivePadding.top + adaptivePadding.bottom + 20)
    }

    private var shouldShowIcon: Bool {
        // Show icons for high cognitive load or executive function support
        cognitiveLoad == .high || cognitiveLoad == .overload ||
            executiveFunctionLevel == .low
    }

    private var priorityIcon: some View {
        switch priority {
        case .low:
            return Image(systemName: "circle")
                .accessibilityLabel("Low priority task - can be done later")
        case .medium:
            return Image(systemName: "circle.fill")
                .accessibilityLabel("Medium priority task - moderate importance")
        case .high:
            return Image(systemName: "exclamationmark.circle.fill")
                .accessibilityLabel("High priority task - needs attention soon")
        case .urgent:
            return Image(systemName: "exclamationmark.triangle.fill")
                .accessibilityLabel("Urgent priority task - requires immediate action")
        }
    }

    private var adaptiveIconSize: Font {
        switch cognitiveLoad {
        case .low: .caption
        case .medium: .body
        case .high: .headline
        case .overload: .title3
        }
    }

    private var adaptiveAnimation: Animation? {
        let motionSensitivity: MotionSensitivity
        switch sensoryPreferences.motionSensitivity {
        case 0.0..<0.2: motionSensitivity = .none
        case 0.2..<0.4: motionSensitivity = .low
        case 0.4..<0.6: motionSensitivity = .medium
        case 0.6..<0.8: motionSensitivity = .high
        default: motionSensitivity = .extreme
        }
        return NeuroNexaDesignSystem.Animation.adaptive(for: motionSensitivity)
    }

    // MARK: - Accessibility

    private var accessibilityLabel: String {
        let cognitiveContext = shouldShowCognitiveContext ? " - Cognitive load: \(cognitiveLoad.rawValue)" : ""
        return "\(title)\(cognitiveContext)"
    }

    private var accessibilityHint: String {
        let priorityHint = switch priority {
        case .low: "Low priority action"
        case .medium: "Medium priority action"
        case .high: "High priority action"
        case .urgent: "Urgent action requiring immediate attention"
        }
        
        let cognitiveHint = getCognitiveHint()
        let interactionHint = "Double tap to activate, or use VoiceOver actions for more options."
        
        return "\(priorityHint). \(cognitiveHint) \(interactionHint)"
    }

    private var accessibilityTraits: AccessibilityTraits {
        var traits: AccessibilityTraits = [.isButton]

        if priority == .urgent {
            traits.insert(.isSelected)
        }

        return traits
    }

    // MARK: - Accessibility Support Methods
    
    private var accessibilityValue: String {
        var components: [String] = []
        
        if shouldShowIcon {
            components.append("Has \(priority.rawValue) priority icon")
        }
        
        if cognitiveLoad == .high || cognitiveLoad == .overload {
            components.append("Optimized for high cognitive load")
        }
        
        return components.isEmpty ? "" : components.joined(separator: ", ")
    }
    
    private var accessibilityInputLabel: String {
        // Simplified label for voice control
        title.lowercased().replacingOccurrences(of: " ", with: "")
    }
    
    private var shouldShowCognitiveContext: Bool {
        cognitiveLoad == .high || cognitiveLoad == .overload
    }
    
    private func getCognitiveHint() -> String {
        switch cognitiveLoad {
        case .low:
            return "Cognitive load is low, full functionality available."
        case .medium:
            return "Cognitive load is moderate, interface adapted for clarity."
        case .high:
            return "Cognitive load is high, interface simplified for easier use."
        case .overload:
            return "Cognitive overload detected, interface optimized for essential actions only."
        }
    }
    
    private func announceActivation() {
        let announcement = "\(title) activated. \(getCognitiveHint())"
        UIAccessibility.post(notification: .announcement, argument: announcement)
    }
    
    private func announceDetailedInfo() {
        let details = """
        Button: \(title). 
        Priority: \(priority.rawValue). 
        Style: \(style). 
        Current cognitive load: \(cognitiveLoad.rawValue). 
        Touch target size: \(Int(minimumTouchTarget)) points.
        """
        UIAccessibility.post(notification: .announcement, argument: details)
    }

    // MARK: - Haptic Feedback

    private func provideHapticFeedback() {
        let intensity = sensoryPreferences.vibrationIntensity.intensity

        if intensity > 0 {
            let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
            impactFeedback.impactOccurred(intensity: CGFloat(intensity))
        }
    }
}

// MARK: - Button Styles

enum CognitiveButtonStyle {
    case primary
    case secondary
    case tertiary
    case destructive
}

// MARK: - Custom Button Style

struct CognitiveButtonPressStyle: ButtonStyle {
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .opacity(configuration.isPressed ? 0.8 : 1.0)
    }
}

// MARK: - Preview

#if DEBUG
@available(iOS 18.0, *)
struct CognitiveButton_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            CognitiveButton("Primary Button", style: .primary, priority: .high) {
                print("Primary button tapped")
            }

            CognitiveButton("Secondary Button", style: .secondary, priority: .medium) {
                print("Secondary button tapped")
            }

            CognitiveButton("Tertiary Button", style: .tertiary, priority: .low) {
                print("Tertiary button tapped")
            }

            CognitiveButton("Destructive Button", style: .destructive, priority: .urgent) {
                print("Destructive button tapped")
            }
        }
        .padding()
        .environment(\.cognitiveLoadLevel, .high)
        .environment(\.sensoryPreferences, .default)
        .environment(\.accessibilitySettings, .default)
        .environment(\.executiveFunctionLevel, .low)
    }
}
#endif
