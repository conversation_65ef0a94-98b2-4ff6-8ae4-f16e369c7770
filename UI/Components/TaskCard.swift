import SwiftUI

// MARK: - Task Card - Neurodiversity-Optimized Task Display Component

/// A card component for displaying AI tasks with cognitive load awareness and executive function support
@available(iOS 26.0, *)
struct TaskCard: View {
    // MARK: - Properties
    let task: AITask
    let onTap: () -> Void
    let onComplete: () -> Void
    let onEdit: (() -> Void)?

    // Environment values for adaptive behavior
    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPreferences
    @Environment(\.accessibilitySettings) private var accessibilitySettings
    @Environment(\.executiveFunctionLevel) private var executiveFunctionLevel

    // State for interaction and animation
    @State private var isPressed = false
    @State private var showingDetails = false
    @State private var progressAnimation = 0.0

    // MARK: - Initialization
    init(
        task: AITask,
        onTap: @escaping () -> Void,
        onComplete: @escaping () -> Void,
        onEdit: (() -> Void)? = nil
    ) {
        self.task = task
        self.onTap = onTap
        self.onComplete = onComplete
        self.onEdit = onEdit
    }

    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: adaptiveSpacing) {
            // Header with title and priority
            headerSection

            // Task description (adaptive based on cognitive load)
            if shouldShowDescription {
                descriptionSection
            }

            // Progress indicator
            if task.progress > 0 {
                progressSection
            }

            // Executive function support section
            if shouldShowExecutiveFunctionSupport {
                executiveFunctionSection
            }

            // Action buttons
            actionButtonsSection
        }
        .padding(adaptivePadding)
        .background(backgroundColor)
        .cornerRadius(adaptiveCornerRadius)
        .neuroNexaShadow(adaptiveShadow)
        .overlay(
            RoundedRectangle(cornerRadius: adaptiveCornerRadius)
                .stroke(borderColor, lineWidth: borderWidth)
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(adaptiveAnimation, value: isPressed)
        .animation(adaptiveAnimation, value: progressAnimation)
        .onTapGesture {
            onTap()
            provideHapticFeedback()
        }
        .onLongPressGesture(minimumDuration: 0.1) {
            // Long press for additional options
            showingDetails.toggle()
        } onPressingChanged: { pressing in
            withAnimation(adaptiveAnimation) {
                isPressed = pressing
            }
        }
        .accessibilityElement(children: .combine)
        .accessibilityLabel(accessibilityLabel)
        .accessibilityHint(accessibilityHint)
        .accessibilityAddTraits(.isButton)
        .onAppear {
            // Animate progress on appear
            withAnimation(.easeInOut(duration: 1.0)) {
                progressAnimation = task.progress
            }
        }
    }

    // MARK: - View Sections

    private var headerSection: some View {
        HStack(alignment: .top, spacing: adaptiveSpacing) {
            // Priority indicator
            priorityIndicator

            VStack(alignment: .leading, spacing: 4) {
                // Task title
                Text(task.title)
                    .font(adaptiveTitleFont)
                    .fontWeight(adaptiveFontWeight)
                    .foregroundColor(titleColor)
                    .lineLimit(cognitiveLoad == .high ? 1 : 2)

                // Metadata row
                metadataRow
            }

            Spacer()

            // Completion status
            completionIndicator
        }
    }

    private var descriptionSection: some View {
        Text(task.description)
            .font(adaptiveBodyFont)
            .foregroundColor(descriptionColor)
            .lineLimit(descriptionLineLimit)
            .padding(.leading, priorityIndicatorWidth + adaptiveSpacing)
    }

    private var progressSection: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("Progress")
                    .font(adaptiveCaptionFont)
                    .foregroundColor(.secondary)

                Spacer()

                Text("\(Int(task.progress * 100))%")
                    .font(adaptiveCaptionFont)
                    .fontWeight(.medium)
                    .foregroundColor(progressColor)
            }

            ProgressView(value: progressAnimation)
                .progressViewStyle(LinearProgressViewStyle())
        }
        .padding(.leading, priorityIndicatorWidth + adaptiveSpacing)
    }

    private var executiveFunctionSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            if !task.aiGeneratedSteps.isEmpty {
                Text("Next Steps")
                    .font(adaptiveCaptionFont)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                ForEach(task.aiGeneratedSteps.prefix(maxVisibleSteps), id: \.id) { step in
                    HStack(spacing: 8) {
                        Image(systemName: step.isCompleted ? "checkmark.circle.fill" : "circle")
                            .foregroundColor(step.isCompleted ? .green : .secondary)
                            .font(.caption)
                            .accessibilityLabel(step.isCompleted ? "Step completed" : "Step not completed")

                        Text(step.title)
                            .font(adaptiveCaptionFont)
                            .foregroundColor(step.isCompleted ? .secondary : .primary)
                            .strikethrough(step.isCompleted)
                    }
                }
            }
        }
        .padding(.leading, priorityIndicatorWidth + adaptiveSpacing)
    }

    private var actionButtonsSection: some View {
        HStack(spacing: adaptiveSpacing) {
            // Complete button
            CognitiveButton(
                task.isCompleted ? "Completed" : "Complete",
                style: task.isCompleted ? .secondary : .primary,
                priority: task.priority
            ) {
                onComplete()
            }
            .disabled(task.isCompleted)

            // Edit button (if provided)
            if let onEdit = onEdit {
                CognitiveButton(
                    "Edit",
                    style: .tertiary,
                    priority: .low
                ) {
                    onEdit()
                }
            }
        }
        .padding(.leading, priorityIndicatorWidth + adaptiveSpacing)
    }

    // MARK: - Helper Views

    private var priorityIndicator: some View {
        RoundedRectangle(cornerRadius: 2)
            .fill(task.priority.color)
            .frame(width: priorityIndicatorWidth, height: priorityIndicatorHeight)
            .accessibilityLabel("\(task.priority.rawValue) priority")
    }

    private var metadataRow: some View {
        HStack(spacing: 12) {
            // Estimated duration
            Label(
                durationText,
                systemImage: "clock"
            )
            .font(adaptiveCaptionFont)
            .foregroundColor(.secondary)

            // Cognitive load indicator
            if shouldShowCognitiveLoad {
                Label(
                    task.cognitiveLoad.rawValue,
                    systemImage: "brain"
                )
                .font(adaptiveCaptionFont)
                .foregroundColor(task.cognitiveLoad.color)
            }
        }
    }

    private var completionIndicator: some View {
        Group {
            if task.isCompleted {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)
                    .accessibilityLabel("Task completed")
            } else {
                Image(systemName: "circle")
                    .foregroundColor(.secondary)
                    .font(.title2)
                    .accessibilityLabel("Task not completed")
            }
        }
    }

    // MARK: - Configuration Helper

    private var configuration: TaskCardConfiguration {
        TaskCardConfiguration(
            task: task,
            cognitiveLoad: cognitiveLoad,
            sensoryPreferences: sensoryPreferences,
            executiveFunctionLevel: executiveFunctionLevel
        )
    }

    // MARK: - Computed Properties

    private var adaptiveSpacing: CGFloat {
        configuration.adaptiveSpacing
    }

    private var adaptivePadding: EdgeInsets {
        configuration.adaptivePadding
    }

    private var adaptiveCornerRadius: CGFloat {
        configuration.adaptiveCornerRadius
    }

    private var adaptiveShadow: Shadow {
        configuration.adaptiveShadow
    }

    private var backgroundColor: Color {
        configuration.backgroundColor
    }

    private var borderColor: Color {
        configuration.borderColor
    }

    private var borderWidth: CGFloat {
        configuration.borderWidth
    }

    private var adaptiveTitleFont: Font {
        configuration.adaptiveTitleFont
    }

    private var adaptiveBodyFont: Font {
        configuration.adaptiveBodyFont
    }

    private var adaptiveCaptionFont: Font {
        configuration.adaptiveCaptionFont
    }

    private var adaptiveFontWeight: Font.Weight {
        configuration.adaptiveFontWeight
    }

    private var titleColor: Color {
        configuration.titleColor
    }

    private var descriptionColor: Color {
        configuration.descriptionColor
    }

    private var progressColor: Color {
        configuration.progressColor
    }

    private var adaptiveAnimation: Animation? {
        configuration.adaptiveAnimation
    }

    // MARK: - Layout Constants

    private var priorityIndicatorWidth: CGFloat {
        configuration.priorityIndicatorWidth
    }

    private var priorityIndicatorHeight: CGFloat {
        configuration.priorityIndicatorHeight
    }

    // MARK: - Conditional Display Logic

    private var shouldShowDescription: Bool {
        configuration.shouldShowDescription
    }

    private var shouldShowCognitiveLoad: Bool {
        configuration.shouldShowCognitiveLoad
    }

    private var shouldShowExecutiveFunctionSupport: Bool {
        configuration.shouldShowExecutiveFunctionSupport
    }

    private var descriptionLineLimit: Int {
        configuration.descriptionLineLimit
    }

    private var maxVisibleSteps: Int {
        configuration.maxVisibleSteps
    }

    private var durationText: String {
        configuration.durationText
    }

    // MARK: - Accessibility

    private var accessibilityLabel: String {
        configuration.accessibilityLabel
    }

    private var accessibilityHint: String {
        configuration.accessibilityHint
    }

    // MARK: - Haptic Feedback

    private func provideHapticFeedback() {
        configuration.provideHapticFeedback()
    }
}

// MARK: - Preview

#if DEBUG
@available(iOS 26.0, *)
struct TaskCard_Previews: PreviewProvider {
    static var previews: some View {
        let sampleTask = AITask(
            title: "Complete project documentation",
            description: "Write comprehensive documentation for the NeuroNexa iOS app including user guides and technical specifications.",
            priority: .high,
            estimatedDuration: 3_600,
            cognitiveLoad: .medium
        )

        VStack(spacing: 20) {
            TaskCard(
                task: sampleTask,
                onTap: { print("Task tapped") },
                onComplete: { print("Task completed") },
                onEdit: { print("Task edited") }
            )
        }
        .padding()
        .environment(\.cognitiveLoadLevel, .medium)
        .environment(\.sensoryPreferences, .default)
        .environment(\.accessibilitySettings, .default)
        .environment(\.executiveFunctionLevel, .low)
    }
}
#endif
