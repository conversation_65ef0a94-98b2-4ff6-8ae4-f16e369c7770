import SwiftUI

// MARK: - Task Card - Neurodiversity-Optimized Task Display Component

/// A card component for displaying AI tasks with cognitive load awareness and executive function support
@available(iOS 18.0, *)
struct TaskCard: View {
    // MARK: - Properties
    let task: AITask
    let onTap: () -> Void
    let onComplete: () -> Void
    let onEdit: (() -> Void)?

    // Environment values for adaptive behavior
    @Environment(\.cognitiveLoadLevel) private var cognitiveLoad
    @Environment(\.sensoryPreferences) private var sensoryPreferences
    @Environment(\.accessibilitySettings) private var accessibilitySettings
    @Environment(\.executiveFunctionLevel) private var executiveFunctionLevel

    // State for interaction and animation
    @State private var isPressed = false
    @State private var showingDetails = false
    @State private var progressAnimation = 0.0

    // MARK: - Initialization
    init(
        task: AITask,
        onTap: @escaping () -> Void,
        onComplete: @escaping () -> Void,
        onEdit: (() -> Void)? = nil
    ) {
        self.task = task
        self.onTap = onTap
        self.onComplete = onComplete
        self.onEdit = onEdit
    }

    // MARK: - Body
    var body: some View {
        VStack(alignment: .leading, spacing: adaptiveSpacing) {
            // Header with title and priority
            headerSection

            // Task description (adaptive based on cognitive load)
            if shouldShowDescription {
                descriptionSection
            }

            // Progress indicator
            if task.progress > 0 {
                progressSection
            }

            // Executive function support section
            if shouldShowExecutiveFunctionSupport {
                executiveFunctionSection
            }

            // Action buttons
            actionButtonsSection
        }
        .padding(adaptivePadding)
        .background(backgroundColor)
        .cornerRadius(adaptiveCornerRadius)
        .neuroNexaShadow(adaptiveShadow)
        .overlay(
            RoundedRectangle(cornerRadius: adaptiveCornerRadius)
                .stroke(borderColor, lineWidth: borderWidth)
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(adaptiveAnimation, value: isPressed)
        .animation(adaptiveAnimation, value: progressAnimation)
        .onTapGesture {
            announceTaskActivation()
            onTap()
            provideHapticFeedback()
        }
        .onLongPressGesture(minimumDuration: 0.1) {
            // Long press for additional options
            withAnimation {
                showingDetails.toggle()
            }
            announceDetailsToggle()
        } onPressingChanged: { pressing in
            withAnimation(adaptiveAnimation) {
                isPressed = pressing
            }
            if pressing {
                UIAccessibility.post(notification: .announcement, argument: "Task card pressed")
            }
        }
        .sensoryFeedback(.selection, trigger: showingDetails)
        .sensoryFeedback(.impact(intensity: 0.3), trigger: onTap)
        .accessibilityElement(children: .combine)
        .accessibilityLabel(accessibilityLabel)
        .accessibilityHint(accessibilityHint)
        .accessibilityValue(accessibilityValue)
        .accessibilityAddTraits([.isButton] + accessibilityTraits)
        .accessibilityIdentifier("TaskCard_\(task.id)")
        .accessibilityRespondsToUserInteraction(true)
        .accessibilityShowsLargeContentViewer()
        .accessibilityInputLabels(["Task \(task.title)", task.title.lowercased()])
        .accessibilityAction(named: "Complete Task") {
            announceTaskCompletion()
            onComplete()
        }
        .accessibilityAction(named: "Edit Task") {
            announceTaskEdit()
            onEdit?()
        }
        .accessibilityAction(named: "Toggle Details") {
            withAnimation {
                showingDetails.toggle()
            }
            announceDetailsToggle()
        }
        .accessibilityAction(named: "Get Task Information") {
            announceDetailedTaskInfo()
        }
        .onAppear {
            // Animate progress on appear
            withAnimation(.easeInOut(duration: 1.0)) {
                progressAnimation = task.progress
            }
            // Announce task appearance for screen readers
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                announceTaskPresence()
            }
        }
        .onChange(of: task.progress) { _, newProgress in
            withAnimation {
                progressAnimation = newProgress
            }
            UIAccessibility.post(
                notification: .announcement,
                argument: "Task progress updated to \(Int(newProgress * 100)) percent"
            )
        }
        .focusable(true)
        .focusEffectDisabled(false)
    }

    // MARK: - View Sections

    private var headerSection: some View {
        HStack(alignment: .top, spacing: adaptiveSpacing) {
            // Priority indicator
            priorityIndicator

            VStack(alignment: .leading, spacing: 4) {
                // Task title
                Text(task.title)
                    .font(adaptiveTitleFont)
                    .fontWeight(adaptiveFontWeight)
                    .foregroundColor(titleColor)
                    .lineLimit(cognitiveLoad == .high ? 1 : 2)

                // Metadata row
                metadataRow
            }

            Spacer()

            // Completion status
            completionIndicator
        }
    }

    private var descriptionSection: some View {
        Text(task.description)
            .font(adaptiveBodyFont)
            .foregroundColor(descriptionColor)
            .lineLimit(descriptionLineLimit)
            .padding(.leading, priorityIndicatorWidth + adaptiveSpacing)
    }

    private var progressSection: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text("Progress")
                    .font(adaptiveCaptionFont)
                    .foregroundColor(.secondary)

                Spacer()

                Text("\(Int(task.progress * 100))%")
                    .font(adaptiveCaptionFont)
                    .fontWeight(.medium)
                    .foregroundColor(progressColor)
            }

            ProgressView(value: progressAnimation)
                .progressViewStyle(LinearProgressViewStyle())
                .tint(Color.neuroNexaPrimary)
                .accessibilityLabel("Task progress: \(Int(task.progress * 100)) percent complete")
        }
        .padding(.leading, priorityIndicatorWidth + adaptiveSpacing)
    }

    private var executiveFunctionSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            if !task.aiGeneratedSteps.isEmpty {
                Text("Next Steps")
                    .font(adaptiveCaptionFont)
                    .fontWeight(.medium)
                    .foregroundColor(.secondary)

                ForEach(Array(task.aiGeneratedSteps.prefix(maxVisibleSteps).enumerated()), id: \.offset) { index, step in
                    HStack(spacing: 8) {
                        Image(systemName: "circle")
                            .foregroundColor(.secondary)
                            .font(.caption)
                            .accessibilityLabel("Step \(index + 1)")

                        Text(step)
                            .font(adaptiveCaptionFont)
                            .foregroundColor(.primary)
                            .lineLimit(1)
                    }
                }
            }
        }
        .padding(.leading, priorityIndicatorWidth + adaptiveSpacing)
    }

    private var actionButtonsSection: some View {
        HStack(spacing: adaptiveSpacing) {
            // Complete button
            Button {
                onComplete()
            } label: {
                HStack {
                    Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                    Text(task.isCompleted ? "Completed" : "Complete")
                }
                .foregroundColor(task.isCompleted ? .secondary : .white)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(task.isCompleted ? Color.gray.opacity(0.3) : Color.neuroNexaPrimary)
                .cornerRadius(8)
            }
            .disabled(task.isCompleted)
            .accessibilityLabel(task.isCompleted ? "Task completed" : "Mark task as complete")
            .sensoryFeedback(.success, trigger: task.isCompleted)

            // Edit button (if provided)
            if let onEdit = onEdit {
                Button {
                    onEdit()
                } label: {
                    Text("Edit")
                        .foregroundColor(.neuroNexaPrimary)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(Color.clear)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color.neuroNexaPrimary, lineWidth: 1)
                        )
                }
                .accessibilityLabel("Edit task")
                .accessibilityHint("Modify task details")
            }
        }
        .padding(.leading, priorityIndicatorWidth + adaptiveSpacing)
    }

    // MARK: - Helper Views

    private var priorityIndicator: some View {
        RoundedRectangle(cornerRadius: 2)
            .fill(task.priority.color)
            .frame(width: priorityIndicatorWidth, height: priorityIndicatorHeight)
            .accessibilityLabel("\(task.priority.rawValue) priority")
    }

    private var metadataRow: some View {
        HStack(spacing: 12) {
            // Estimated duration
            Label(
                durationText,
                systemImage: "clock"
            )
            .font(adaptiveCaptionFont)
            .foregroundColor(.secondary)

            // Cognitive load indicator
            if shouldShowCognitiveLoad {
                Label(
                    task.cognitiveLoad.rawValue,
                    systemImage: "brain"
                )
                .font(adaptiveCaptionFont)
                .foregroundColor(task.cognitiveLoad.color)
            }
        }
    }

    private var completionIndicator: some View {
        Group {
            if task.isCompleted {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                    .font(.title2)
                    .accessibilityLabel("Task completed")
            } else {
                Image(systemName: "circle")
                    .foregroundColor(.secondary)
                    .font(.title2)
                    .accessibilityLabel("Task not completed")
            }
        }
    }

    // MARK: - Configuration Helper

    private var configuration: TaskCardConfiguration {
        TaskCardConfiguration(
            task: task,
            cognitiveLoad: cognitiveLoad,
            sensoryPreferences: sensoryPreferences,
            executiveFunctionLevel: executiveFunctionLevel
        )
    }

    // MARK: - Computed Properties

    // MARK: - Configuration forwarding properties moved to TaskCard+Configuration extension

    // MARK: - Layout Constants

    // MARK: - All remaining configuration properties moved to TaskCard+Configuration extension

    // MARK: - Accessibility Support Methods
    
    private var accessibilityValue: String {
        var components: [String] = []
        
        components.append(task.isCompleted ? "Completed" : "Not completed")
        
        if task.progress > 0 {
            components.append("\(Int(task.progress * 100)) percent complete")
        }
        
        components.append("Priority: \(task.priority.rawValue)")
        
        if let duration = task.estimatedDuration {
            components.append("Estimated time: \(Int(duration / 60)) minutes")
        }
        
        if cognitiveLoad == .high || cognitiveLoad == .overload {
            components.append("Interface adapted for high cognitive load")
        }
        
        return components.joined(separator: ", ")
    }
    
    private var accessibilityTraits: AccessibilityTraits {
        var traits: AccessibilityTraits = [.isButton]
        
        if task.isCompleted {
            traits.insert(.notEnabled)
        }
        
        if task.priority == .urgent {
            traits.insert(.isSelected)
        }
        
        if task.progress > 0 {
            traits.insert(.updatesFrequently)
        }
        
        return traits
    }
    
    private func announceTaskActivation() {
        let announcement = "Task \(task.title) activated. \(task.isCompleted ? "Task is completed" : "Task is active")."
        UIAccessibility.post(notification: .announcement, argument: announcement)
    }
    
    private func announceTaskCompletion() {
        let announcement = task.isCompleted ? "Task already completed" : "Marking task as complete"
        UIAccessibility.post(notification: .announcement, argument: announcement)
    }
    
    private func announceTaskEdit() {
        UIAccessibility.post(
            notification: .announcement,
            argument: "Opening task editor for \(task.title)"
        )
    }
    
    private func announceDetailsToggle() {
        let state = showingDetails ? "expanded" : "collapsed"
        UIAccessibility.post(
            notification: .announcement,
            argument: "Task details \(state)"
        )
    }
    
    private func announceTaskPresence() {
        let status = task.isCompleted ? "completed" : "active"
        let priority = task.priority.rawValue
        UIAccessibility.post(
            notification: .announcement,
            argument: "\(priority) priority task: \(task.title), \(status)"
        )
    }
    
    private func announceDetailedTaskInfo() {
        var details = ["Task: \(task.title)"]
        
        if !task.description.isEmpty {
            details.append("Description: \(task.description)")
        }
        
        details.append("Priority: \(task.priority.rawValue)")
        details.append("Status: \(task.isCompleted ? "Completed" : "Active")")
        
        if task.progress > 0 {
            details.append("Progress: \(Int(task.progress * 100)) percent")
        }
        
        if let duration = task.estimatedDuration {
            details.append("Estimated duration: \(Int(duration / 60)) minutes")
        }
        
        details.append("Current cognitive load: \(cognitiveLoad.rawValue)")
        
        UIAccessibility.post(
            notification: .announcement,
            argument: details.joined(separator: ", ")
        )
    }

    // MARK: - Haptic Feedback

    private func provideHapticFeedback() {
        configuration.provideHapticFeedback()
    }
}

// MARK: - Preview

#if DEBUG
@available(iOS 18.0, *)
struct TaskCard_Previews: PreviewProvider {
    static var previews: some View {
        let sampleTask = AITask(
            title: "Complete project documentation",
            description: "Write comprehensive documentation for the NeuroNexa iOS app including user guides and technical specifications.",
            priority: .high,
            estimatedDuration: 3_600,
            cognitiveLoad: .medium
        )

        VStack(spacing: 20) {
            TaskCard(
                task: sampleTask,
                onTap: { print("Task tapped") },
                onComplete: { print("Task completed") },
                onEdit: { print("Task edited") }
            )
        }
        .padding()
        .environment(\.cognitiveLoadLevel, .medium)
        .environment(\.sensoryPreferences, .default)
        .environment(\.accessibilitySettings, .default)
        .environment(\.executiveFunctionLevel, .low)
    }
}
#endif
