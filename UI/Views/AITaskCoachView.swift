import SwiftUI

@available(iOS 18.0, *)
struct AITaskCoachView: View {
    // Claude: MEMORY - Use ObservedObject for injected ViewModels to prevent recreation
    @ObservedObject var viewModel: AITaskCoachViewModel
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Header Section
                    headerSection

                    // AI Suggestions
                    if !viewModel.aiSuggestions.isEmpty {
                        aiSuggestionsSection
                    }

                    // Task Input Section
                    taskInputSection

                    // Generated Tasks
                    if !viewModel.generatedTasks.isEmpty {
                        generatedTasksSection
                    }

                    // Cognitive Load Insights
                    cognitiveInsightsSection
                }
                .padding()
            }
            .navigationTitle("AI Task Coach")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
            .background(Color.neuroNexaBackground.ignoresSafeArea())
        }
        .onAppear {
            Task {
                await viewModel.loadAISuggestions()
            }
        }
        // Claude: ACCESSIBILITY - Add semantic container for complex view hierarchy
        .accessibilityElement(children: .contain)
        .accessibilityLabel("AI Task Coach Interface")
        .accessibilityHint("Create and manage AI-generated tasks with cognitive load optimization")
    }

    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "brain.head.profile")
                    .font(.largeTitle)
                    .foregroundColor(.neuroNexaPrimary)
                    .accessibilityLabel("AI Task Coach - brain icon for cognitive support")

                VStack(alignment: .leading) {
                    Text("AI Task Coach")
                        .font(.title2)
                        .fontWeight(.bold)
                        .foregroundColor(.neuroNexaText)

                    Text("Personalized task assistance for your neurodivergent mind")
                        .font(.body)
                        .foregroundColor(.neuroNexaTextSecondary)
                }

                Spacer()
            }

            // Current cognitive state
            CognitiveStateCard(state: viewModel.currentCognitiveState)
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(16)
        .cognitiveLoadOptimized()
    }

    private var aiSuggestionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("AI Suggestions")
                .font(.headline)
                .foregroundColor(.neuroNexaText)

            ForEach(viewModel.aiSuggestions) { suggestion in
                AISuggestionCard(suggestion: suggestion) {
                    Task {
                        await viewModel.applySuggestion(suggestion)
                    }
                }
            }
        }
    }

    private var taskInputSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Describe Your Goal")
                .font(.headline)
                .foregroundColor(.neuroNexaText)

            VStack(spacing: 12) {
                TextField("What would you like to accomplish?", text: $viewModel.taskInput, axis: .vertical)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .lineLimit(3...6)

                HStack {
                    Picker("Priority", selection: $viewModel.selectedPriority) {
                        ForEach(TaskPriority.allCases, id: \.self) { priority in
                            Text(priority.rawValue.capitalized).tag(priority)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())

                    Spacer()
                }

                Button {
                    Task {
                        await viewModel.generateTasks()
                    }
                } label: {
                    HStack {
                        if viewModel.isGenerating {
                            ProgressView()
                                .scaleEffect(0.8)
                        } else {
                            Image(systemName: "sparkles")
                                .accessibilityLabel("Generate AI tasks - create personalized task suggestions")
                        }

                        Text(viewModel.isGenerating ? "Generating..." : "Generate Tasks")
                    }
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.neuroNexaPrimary)
                    .foregroundColor(.white)
                    .cornerRadius(12)
                }
                .disabled(viewModel.taskInput.isEmpty || viewModel.isGenerating)
                // Claude: ACCESSIBILITY - Add haptic feedback for neurodiversity support
                .accessibilityAddTraits(.isButton)
                .accessibilityHint("Double tap to generate personalized tasks based on your input")
            }
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(16)
    }

    private var generatedTasksSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Generated Tasks")
                    .font(.headline)
                    .foregroundColor(.neuroNexaText)

                Spacer()

                Button("Save All") {
                    Task {
                        await viewModel.saveAllTasks()
                    }
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.neuroNexaPrimary)
                .foregroundColor(.white)
                .cornerRadius(8)
            }

            ForEach(viewModel.generatedTasks) { task in
                GeneratedTaskCard(task: task) { updatedTask in
                    viewModel.updateTask(updatedTask)
                }
            }
        }
    }

    private var cognitiveInsightsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Cognitive Insights")
                .font(.headline)
                .foregroundColor(.neuroNexaText)

            if let insights = viewModel.cognitiveInsights {
                CognitiveInsightsCard(insights: insights)
            } else {
                Text("Generate tasks to see personalized insights")
                    .font(.body)
                    .foregroundColor(.neuroNexaTextSecondary)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.neuroNexaSurface.opacity(0.5))
                    .cornerRadius(12)
            }
        }
    }
}

// MARK: - Supporting Views

@available(iOS 18.0, *)
struct CognitiveStateCard: View {
    let state: CognitiveState

    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text("Current State")
                    .font(.caption)
                    .foregroundColor(.neuroNexaTextSecondary)

                Text(state.description)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.neuroNexaText)
            }

            Spacer()

            Circle()
                .fill(state.color)
                .frame(width: 12, height: 12)
        }
        .padding()
        .background(state.color.opacity(0.1))
        .cornerRadius(12)
    }
}

@available(iOS 18.0, *)
struct AISuggestionCard: View {
    let suggestion: AISuggestion
    let onApply: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: suggestion.icon)
                    .foregroundColor(.neuroNexaPrimary)
                    .accessibilityLabel("\(suggestion.title) suggestion icon")

                VStack(alignment: .leading, spacing: 4) {
                    Text(suggestion.title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.neuroNexaText)

                    Text(suggestion.description)
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }

                Spacer()

                Button("Apply") {
                    onApply()
                }
                .font(.caption)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.neuroNexaPrimary)
                .foregroundColor(.white)
                .cornerRadius(8)
            }
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
        .sensoryAdaptive()
    }
}

@available(iOS 18.0, *)
struct GeneratedTaskCard: View {
    let task: AITask
    let onUpdate: (AITask) -> Void

    @State private var isExpanded = false

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(task.title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.neuroNexaText)

                    if !task.description.isEmpty {
                        Text(task.description)
                            .font(.caption)
                            .foregroundColor(.neuroNexaTextSecondary)
                            .lineLimit(isExpanded ? nil : 2)
                    }
                }

                Spacer()

                VStack(spacing: 8) {
                    CognitiveLoadBadge(level: task.cognitiveLoad)

                    Button {
                        isExpanded.toggle()
                    } label: {
                        Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                            .foregroundColor(.neuroNexaSecondary)
                            .accessibilityLabel(isExpanded ? "Collapse task details" : "Expand task details")
                    }
                }
            }

            if isExpanded {
                VStack(alignment: .leading, spacing: 8) {
                    if !task.aiGeneratedSteps.isEmpty {
                        Text("Steps:")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.neuroNexaText)

                        ForEach(Array(task.aiGeneratedSteps.enumerated()), id: \.offset) { index, step in
                            HStack(alignment: .top) {
                                Text("\(index + 1).")
                                    .font(.caption)
                                    .foregroundColor(.neuroNexaTextSecondary)

                                Text(step)
                                    .font(.caption)
                                    .foregroundColor(.neuroNexaTextSecondary)
                            }
                        }
                    }

                    HStack {
                        Text("Estimated: \(Int(task.estimatedDuration / 60)) min")
                            .font(.caption)
                            .foregroundColor(.neuroNexaTextSecondary)

                        Spacer()

                        Button("Save Task") {
                            // Save individual task
                        }
                        .font(.caption)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.neuroNexaSecondary)
                        .foregroundColor(.white)
                        .cornerRadius(6)
                    }
                }
            }
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
    }
}

@available(iOS 18.0, *)
struct CognitiveInsightsCard: View {
    let insights: CognitiveInsights

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Personalized Insights")
                .font(.body)
                .fontWeight(.medium)
                .foregroundColor(.neuroNexaText)

            ForEach(insights.recommendations, id: \.self) { recommendation in
                HStack(alignment: .top) {
                    Image(systemName: "lightbulb")
                        .foregroundColor(.neuroNexaAccent)
                        .frame(width: 16)
                        .accessibilityLabel("Insight recommendation - helpful tip")

                    Text(recommendation)
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
            }

            if let breakSuggestion = insights.breakSuggestion {
                Divider()

                HStack {
                    Image(systemName: "pause.circle")
                        .foregroundColor(.orange)
                        .accessibilityLabel("Break suggestion - time to rest and recharge")

                    Text(breakSuggestion)
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
            }
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
    }
}

// MARK: - Preview

@available(iOS 18.0, *)
#Preview {
    AITaskCoachView(viewModel: AITaskCoachViewModel(
        aiTaskCoach: OpenAITaskCoach(),
        userService: UserService(),
        taskService: TaskService()
    ))
}
