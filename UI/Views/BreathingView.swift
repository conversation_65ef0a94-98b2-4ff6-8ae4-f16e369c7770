import SwiftUI

@available(iOS 18.0, *)
struct BreathingView: View {
    @StateObject private var viewModel = BreathingViewModel(breathingService: BreathingService())
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ZStack {
                Color.neuroNexaBackground.ignoresSafeArea()

                VStack(spacing: 30) {
                    // Header
                    headerSection

                    Spacer()

                    // Breathing Animation
                    breathingAnimationSection

                    Spacer()

                    // Instructions
                    instructionsSection

                    // Controls
                    controlsSection
                }
                .padding()
            }
            .navigationTitle("Breathing Exercise")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Close") {
                        viewModel.stopSession()
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    But<PERSON>("Settings") {
                        viewModel.showSettings = true
                    }
                }
            }
        }
        .onAppear {
            Task {
                await viewModel.loadBreathingExercises()
            }
        }
        .sheet(isPresented: $viewModel.showSettings) {
            BreathingSettingsView(viewModel: viewModel)
        }
    }

    private var headerSection: some View {
        VStack(spacing: 12) {
            Text(viewModel.currentExercise?.name ?? "Select Exercise")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(.neuroNexaText)

            if let exercise = viewModel.currentExercise {
                Text(exercise.description)
                    .font(.body)
                    .foregroundColor(.neuroNexaTextSecondary)
                    .multilineTextAlignment(.center)
            }

            // Session Progress
            if viewModel.isSessionActive {
                VStack(spacing: 8) {
                    Text("Session Progress")
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)

                    ProgressView(value: viewModel.sessionProgress)
                        .progressViewStyle(LinearProgressViewStyle(tint: .neuroNexaPrimary))

                    Text("\(Int(viewModel.elapsedTime / 60)):\(String(format: "%02d", Int(viewModel.elapsedTime) % 60))")
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
            }
        }
    }

    private var breathingAnimationSection: some View {
        ZStack {
            // Outer breathing circle
            Circle()
                .stroke(Color.neuroNexaPrimary.opacity(0.3), lineWidth: 2)
                .frame(width: 200, height: 200)

            // Inner breathing circle
            Circle()
                .fill(
                    RadialGradient(
                        gradient: Gradient(colors: [
                            Color.neuroNexaPrimary.opacity(0.8),
                            Color.neuroNexaPrimary.opacity(0.4)
                        ]),
                        center: .center,
                        startRadius: 20,
                        endRadius: 100
                    )
                )
                .frame(width: viewModel.circleSize, height: viewModel.circleSize)
                .scaleEffect(viewModel.breathingScale)
                .animation(
                    viewModel.isSessionActive ?
                        .easeInOut(duration: viewModel.breathingDuration) : .none,
                    value: viewModel.breathingScale
                )

            // Breathing guide text
            VStack(spacing: 8) {
                Text(viewModel.breathingPhase.rawValue)
                    .font(.title3)
                    .fontWeight(.medium)
                    .foregroundColor(.white)

                if viewModel.isSessionActive {
                    Text("\(viewModel.phaseCountdown)")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.white)
                        .contentTransition(.numericText())
                }
            }
        }
    }

    private var instructionsSection: some View {
        VStack(spacing: 16) {
            if let exercise = viewModel.currentExercise {
                Text("Instructions")
                    .font(.headline)
                    .foregroundColor(.neuroNexaText)

                Text(exercise.instructions)
                    .font(.body)
                    .foregroundColor(.neuroNexaTextSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
            }

            // Breathing pattern info
            if viewModel.isSessionActive {
                HStack(spacing: 20) {
                    PatternInfoView(
                        title: "Inhale",
                        duration: viewModel.currentPattern.inhale,
                        color: .blue
                    )

                    if viewModel.currentPattern.hold > 0 {
                        PatternInfoView(
                            title: "Hold",
                            duration: viewModel.currentPattern.hold,
                            color: .orange
                        )
                    }

                    PatternInfoView(
                        title: "Exhale",
                        duration: viewModel.currentPattern.exhale,
                        color: .green
                    )

                    if viewModel.currentPattern.pause > 0 {
                        PatternInfoView(
                            title: "Pause",
                            duration: viewModel.currentPattern.pause,
                            color: .purple
                        )
                    }
                }
                .padding()
                .background(Color.neuroNexaSurface.opacity(0.5))
                .cornerRadius(12)
            }
        }
    }

    private var controlsSection: some View {
        VStack(spacing: 16) {
            // Exercise Selection
            if !viewModel.isSessionActive {
                Picker("Exercise", selection: $viewModel.selectedExerciseIndex) {
                    ForEach(Array(viewModel.availableExercises.enumerated()), id: \.offset) { index, exercise in
                        Text(exercise.name).tag(index)
                    }
                }
                .pickerStyle(MenuPickerStyle())
                .padding()
                .background(Color.neuroNexaSurface)
                .cornerRadius(12)
            }

            // Main Control Button
            Button {
                if viewModel.isSessionActive {
                    if viewModel.isPaused {
                        viewModel.resumeSession()
                    } else {
                        viewModel.pauseSession()
                    }
                } else {
                    Task {
                        await viewModel.startSession()
                    }
                }
            } label: {
                HStack {
                    Image(systemName: viewModel.controlButtonIcon)
                        .accessibilityLabel("Breathing exercise control: \(viewModel.controlButtonText) - helps with anxiety and stress management")
                    Text(viewModel.controlButtonText)
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(viewModel.controlButtonColor)
                .foregroundColor(.white)
                .cornerRadius(12)
            }

            // Secondary Controls
            if viewModel.isSessionActive {
                HStack(spacing: 16) {
                    Button("Stop") {
                        viewModel.stopSession()
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.red.opacity(0.8))
                    .foregroundColor(.white)
                    .cornerRadius(8)

                    Button("Extend") {
                        viewModel.extendSession()
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 10)
                    .background(Color.neuroNexaSecondary)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
            }
        }
    }
}

// MARK: - Supporting Views

@available(iOS 18.0, *)
struct PatternInfoView: View {
    let title: String
    let duration: Double
    let color: Color

    var body: some View {
        VStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(color)

            Text("\(Int(duration))s")
                .font(.caption2)
                .foregroundColor(.neuroNexaTextSecondary)
        }
    }
}

@available(iOS 18.0, *)
struct BreathingSettingsView: View {
    @ObservedObject var viewModel: BreathingViewModel
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            Form {
                Section("Session Duration") {
                    Picker("Duration", selection: $viewModel.sessionDuration) {
                        Text("2 minutes").tag(120.0)
                        Text("5 minutes").tag(300.0)
                        Text("10 minutes").tag(600.0)
                        Text("15 minutes").tag(900.0)
                        Text("20 minutes").tag(1_200.0)
                    }
                }

                Section("Breathing Speed") {
                    Picker("Speed", selection: $viewModel.breathingSpeed) {
                        Text("Slow").tag(BreathingSpeed.slow)
                        Text("Normal").tag(BreathingSpeed.normal)
                        Text("Fast").tag(BreathingSpeed.fast)
                    }
                }

                Section("Audio Guidance") {
                    Toggle("Voice Instructions", isOn: $viewModel.voiceGuidanceEnabled)
                    Toggle("Background Sounds", isOn: $viewModel.backgroundSoundsEnabled)

                    if viewModel.backgroundSoundsEnabled {
                        Picker("Sound", selection: $viewModel.selectedBackgroundSound) {
                            Text("Ocean Waves").tag("ocean")
                            Text("Forest Rain").tag("rain")
                            Text("White Noise").tag("white_noise")
                            Text("None").tag("none")
                        }
                    }
                }

                Section("Accessibility") {
                    Toggle("Haptic Feedback", isOn: $viewModel.hapticFeedbackEnabled)
                    Toggle("Visual Cues Only", isOn: $viewModel.visualCuesOnly)
                    Toggle("Reduce Motion", isOn: $viewModel.reduceMotion)
                }
            }
            .navigationTitle("Breathing Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

// MARK: - Preview

@available(iOS 18.0, *)
#Preview {
    BreathingView()
}
