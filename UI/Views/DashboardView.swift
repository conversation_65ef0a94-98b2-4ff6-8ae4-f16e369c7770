import SwiftUI

@available(iOS 18.0, *)
struct DashboardView: View {
    @StateObject private var viewModel = DashboardViewModel()
    @Environment(\.colorScheme) var colorScheme

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Welcome Section
                    welcomeSection

                    // Quick Actions
                    quickActionsSection

                    // Today's Tasks
                    todaysTasksSection

                    // Cognitive Load Monitor
                    cognitiveLoadSection

                    // Recent Activity
                    recentActivitySection
                }
                .padding()
            }
            .navigationTitle("NeuroNexa")
            .navigationBarTitleDisplayMode(.large)
            .background(Color.neuroNexaBackground.ignoresSafeArea())
        }
        .onAppear {
            Task {
                await viewModel.loadDashboardData()
            }
        }
    }

    private var welcomeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading) {
                    Text("Good \(timeOfDayGreeting)")
                        .font(.title2)
                        .foregroundColor(.neuroNexaTextSecondary)

                    Text(viewModel.userName)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                        .foregroundColor(.neuroNexaText)
                }

                Spacer()

                // Cognitive status indicator
                cognitiveStatusIndicator
            }

            Text("How are you feeling today?")
                .font(.body)
                .foregroundColor(.neuroNexaTextSecondary)
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(16)
        .cognitiveLoadOptimized()
    }

    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Quick Actions")
                .font(.headline)
                .foregroundColor(.neuroNexaText)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                QuickActionCard(
                    title: "AI Task Coach",
                    icon: "brain.head.profile",
                    color: .neuroNexaPrimary
                ) {
                    // Navigate to AI Task Coach
                }

                QuickActionCard(
                    title: "Breathing Exercise",
                    icon: "lungs",
                    color: .neuroNexaSecondary
                ) {
                    // Navigate to Breathing Exercise
                }

                QuickActionCard(
                    title: "Focus Timer",
                    icon: "timer",
                    color: .neuroNexaAccent
                ) {
                    // Navigate to Focus Timer
                }

                QuickActionCard(
                    title: "Mood Check",
                    icon: "heart.circle",
                    color: .pink
                ) {
                    // Navigate to Mood Check
                }
            }
        }
    }

    private var todaysTasksSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("Today's Tasks")
                    .font(.headline)
                    .foregroundColor(.neuroNexaText)

                Spacer()

                Button("View All") {
                    // Navigate to all tasks
                }
                .font(.caption)
                .foregroundColor(.neuroNexaPrimary)
            }

            if viewModel.todaysTasks.isEmpty {
                EmptyTasksView()
            } else {
                ForEach(viewModel.todaysTasks.prefix(3)) { task in
                    TaskRowView(task: task)
                }
            }
        }
    }

    private var cognitiveLoadSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Cognitive Load Monitor")
                .font(.headline)
                .foregroundColor(.neuroNexaText)

            CognitiveLoadIndicator(level: viewModel.currentCognitiveLoad)

            if viewModel.shouldSuggestBreak {
                BreakSuggestionCard()
            }
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(16)
    }

    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Recent Activity")
                .font(.headline)
                .foregroundColor(.neuroNexaText)

            ForEach(viewModel.recentActivities.prefix(3)) { activity in
                ActivityRowView(activity: activity)
            }
        }
    }

    private var cognitiveStatusIndicator: some View {
        Circle()
            .fill(cognitiveStatusColor)
            .frame(width: 12, height: 12)
            .overlay(
                Circle()
                    .stroke(cognitiveStatusColor.opacity(0.3), lineWidth: 4)
                    .scaleEffect(1.5)
            )
    }

    private var cognitiveStatusColor: Color {
        switch viewModel.currentCognitiveLoad {
        case .low:
            return .green
        case .medium:
            return .orange
        case .high:
            return .red
        }
    }

    private var timeOfDayGreeting: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 5..<12:
            return "Morning"
        case 12..<17:
            return "Afternoon"
        case 17..<21:
            return "Evening"
        default:
            return "Night"
        }
    }
}

// MARK: - Supporting Views

@available(iOS 18.0, *)
struct QuickActionCard: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .accessibilityLabel("\(title) - tap to access this feature")

                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.neuroNexaText)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(Color.neuroNexaSurface)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
        .sensoryAdaptive()
    }
}

@available(iOS 18.0, *)
struct EmptyTasksView: View {
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "checkmark.circle")
                .font(.largeTitle)
                .foregroundColor(.neuroNexaSecondary)
                .accessibilityLabel("All tasks completed - you're doing great!")

            Text("No tasks for today")
                .font(.body)
                .foregroundColor(.neuroNexaTextSecondary)

            Text("Great job staying on top of things!")
                .font(.caption)
                .foregroundColor(.neuroNexaTextSecondary)
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(Color.neuroNexaSurface.opacity(0.5))
        .cornerRadius(12)
    }
}

@available(iOS 18.0, *)
struct TaskRowView: View {
    let task: AITask

    var body: some View {
        HStack {
            Button {
                // Toggle task completion
            } label: {
                Image(systemName: task.isCompleted ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(task.isCompleted ? .green : .neuroNexaSecondary)
                    .accessibilityLabel(task.isCompleted ? "Task completed - well done!" : "Mark task as complete")
            }

            VStack(alignment: .leading, spacing: 4) {
                Text(task.title)
                    .font(.body)
                    .foregroundColor(.neuroNexaText)
                    .strikethrough(task.isCompleted)

                if !task.description.isEmpty {
                    Text(task.description)
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                        .lineLimit(2)
                }
            }

            Spacer()

            CognitiveLoadBadge(level: task.cognitiveLoad)
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
    }
}

@available(iOS 18.0, *)
struct CognitiveLoadIndicator: View {
    let level: CognitiveLoadLevel

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text("Current Level: \(level.rawValue.capitalized)")
                    .font(.body)
                    .foregroundColor(.neuroNexaText)

                Spacer()

                CognitiveLoadBadge(level: level)
            }

            ProgressView(value: progressValue)
                .progressViewStyle(LinearProgressViewStyle(tint: progressColor))
        }
    }

    private var progressValue: Double {
        switch level {
        case .low: return 0.3
        case .medium: return 0.6
        case .high: return 0.9
        }
    }

    private var progressColor: Color {
        switch level {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        }
    }
}

@available(iOS 18.0, *)
struct CognitiveLoadBadge: View {
    let level: CognitiveLoadLevel

    var body: some View {
        Text(level.rawValue.capitalized)
            .font(.caption2)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 4)
            .background(badgeColor.opacity(0.2))
            .foregroundColor(badgeColor)
            .cornerRadius(8)
    }

    private var badgeColor: Color {
        switch level {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        }
    }
}

@available(iOS 18.0, *)
struct BreakSuggestionCard: View {
    var body: some View {
        HStack {
            Image(systemName: "pause.circle.fill")
                .foregroundColor(.orange)
                .accessibilityLabel("Break suggestion - take time to recharge")

            VStack(alignment: .leading, spacing: 4) {
                Text("Time for a break?")
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.neuroNexaText)

                Text("Your cognitive load is high. Consider taking a short break.")
                    .font(.caption)
                    .foregroundColor(.neuroNexaTextSecondary)
            }

            Spacer()

            Button("Take Break") {
                // Navigate to break activities
            }
            .font(.caption)
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .background(Color.orange)
            .foregroundColor(.white)
            .cornerRadius(8)
        }
        .padding()
        .background(Color.orange.opacity(0.1))
        .cornerRadius(12)
    }
}

@available(iOS 18.0, *)
struct ActivityRowView: View {
    let activity: RecentActivity

    var body: some View {
        HStack {
            Image(systemName: activity.icon)
                .foregroundColor(.neuroNexaSecondary)
                .frame(width: 24)
                .accessibilityLabel("\(activity.title) activity icon")

            VStack(alignment: .leading, spacing: 2) {
                Text(activity.title)
                    .font(.body)
                    .foregroundColor(.neuroNexaText)

                Text(activity.timestamp, style: .relative)
                    .font(.caption)
                    .foregroundColor(.neuroNexaTextSecondary)
            }

            Spacer()
        }
        .padding(.vertical, 8)
    }
}

// MARK: - Preview

@available(iOS 18.0, *)
#Preview {
    DashboardView()
}
