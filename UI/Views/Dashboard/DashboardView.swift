import SwiftUI

@available(iOS 18.0, *)
struct DashboardView: View {
    // Claude: FUNCTIONALITY - Add proper state management for dashboard
    @StateObject private var viewModel = DashboardViewModel()
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        NavigationView {
            ScrollView {
                LazyVStack(spacing: 20) {
                    // Welcome Section
                    welcomeSection
                    
                    // Quick Actions
                    quickActionsSection
                    
                    // Today's Focus
                    todaysFocusSection
                    
                    // Cognitive Status
                    cognitiveStatusSection
                    
                    // Recent Activity
                    recentActivitySection
                }
                .padding()
            }
            .navigationTitle("Dashboard")
            .navigationBarTitleDisplayMode(.large)
            .background(Color.neuroNexaBackground.ignoresSafeArea())
            .sensoryAdaptive()
            .refreshable {
                await viewModel.refreshData()
            }
        }
        .onAppear {
            Task {
                await viewModel.loadDashboardData()
            }
        }
        // Claude: ACCESSIBILITY - Add semantic container for dashboard
        .accessibilityElement(children: .contain)
        .accessibilityLabel("NeuroNexa Dashboard")
        .accessibilityHint("Overview of your productivity and wellness metrics")
    }
    
    private var welcomeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("Good \(viewModel.timeOfDayGreeting)")
                        .font(.title2)
                        .fontWeight(.medium)
                        .foregroundColor(.neuroNexaText)
                    
                    Text("Ready to make today productive?")
                        .font(.body)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
                
                Spacer()
                
                // Weather/Environment indicator
                Image(systemName: viewModel.environmentIcon)
                    .font(.title2)
                    .foregroundColor(.neuroNexaPrimary)
                    .accessibilityLabel("Environment status: \(viewModel.environmentStatus)")
            }
            
            // Streak indicator
            HStack {
                Image(systemName: "flame.fill")
                    .foregroundColor(.orange)
                    .accessibilityLabel("Productivity streak")
                
                Text("\(viewModel.currentStreak) day streak")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.neuroNexaText)
            }
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(16)
        .cognitiveLoadOptimized()
    }
    
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Actions")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                QuickActionCard(
                    icon: "brain.head.profile",
                    title: "AI Coach",
                    subtitle: "Get task suggestions",
                    color: .neuroNexaPrimary
                ) {
                    // Handle AI Coach tap
                }
                
                QuickActionCard(
                    icon: "lungs.fill",
                    title: "Breathe",
                    subtitle: "Quick breathing exercise",
                    color: .blue
                ) {
                    // Handle breathing tap
                }
                
                QuickActionCard(
                    icon: "calendar",
                    title: "Plan Day",
                    subtitle: "Build today's routine",
                    color: .green
                ) {
                    // Handle routine tap
                }
                
                QuickActionCard(
                    icon: "chart.line.uptrend.xyaxis",
                    title: "Progress",
                    subtitle: "View your metrics",
                    color: .purple
                ) {
                    // Handle progress tap
                }
            }
        }
    }
    
    private var todaysFocusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Today's Focus")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
            
            if viewModel.todaysTasks.isEmpty {
                Text("No tasks scheduled for today")
                    .font(.body)
                    .foregroundColor(.neuroNexaTextSecondary)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.neuroNexaSurface.opacity(0.5))
                    .cornerRadius(12)
            } else {
                ForEach(viewModel.todaysTasks.prefix(3)) { task in
                    TodaysTaskCard(task: task)
                }
            }
        }
    }
    
    private var cognitiveStatusSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Cognitive Status")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
            
            HStack(spacing: 16) {
                CognitiveMetricCard(
                    title: "Energy",
                    value: viewModel.energyLevel,
                    icon: "bolt.fill",
                    color: .yellow
                )
                
                CognitiveMetricCard(
                    title: "Focus",
                    value: viewModel.focusLevel,
                    icon: "target",
                    color: .blue
                )
                
                CognitiveMetricCard(
                    title: "Stress",
                    value: viewModel.stressLevel,
                    icon: "heart.fill",
                    color: .red
                )
            }
        }
    }
    
    private var recentActivitySection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Recent Activity")
                .font(.headline)
                .foregroundColor(.neuroNexaText)
            
            if viewModel.recentActivities.isEmpty {
                Text("No recent activity")
                    .font(.body)
                    .foregroundColor(.neuroNexaTextSecondary)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color.neuroNexaSurface.opacity(0.5))
                    .cornerRadius(12)
            } else {
                ForEach(viewModel.recentActivities.prefix(3)) { activity in
                    ActivityCard(activity: activity)
                }
            }
        }
    }
}

// MARK: - Supporting Views

@available(iOS 18.0, *)
struct QuickActionCard: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .accessibilityLabel("\(title) quick action")
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.body)
                        .fontWeight(.medium)
                        .foregroundColor(.neuroNexaText)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
                
                Spacer()
            }
            .padding()
            .frame(maxWidth: .infinity, minHeight: 80)
            .background(Color.neuroNexaSurface)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

@available(iOS 18.0, *)
struct TodaysTaskCard: View {
    let task: DashboardTask
    
    var body: some View {
        HStack {
            Circle()
                .fill(task.isCompleted ? Color.green : Color.neuroNexaSecondary)
                .frame(width: 8, height: 8)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(task.title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.neuroNexaText)
                    .strikethrough(task.isCompleted)
                
                if let timeEstimate = task.estimatedDuration {
                    Text("\(Int(timeEstimate / 60)) min")
                        .font(.caption)
                        .foregroundColor(.neuroNexaTextSecondary)
                }
            }
            
            Spacer()
            
            if task.priority == .high {
                Image(systemName: "exclamationmark.circle.fill")
                    .foregroundColor(.red)
                    .accessibilityLabel("High priority task")
            }
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
    }
}

@available(iOS 18.0, *)
struct CognitiveMetricCard: View {
    let title: String
    let value: Double
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(color)
                .accessibilityLabel("\(title) metric")
            
            Text(title)
                .font(.caption)
                .foregroundColor(.neuroNexaTextSecondary)
            
            Text("\(Int(value * 100))%")
                .font(.body)
                .fontWeight(.bold)
                .foregroundColor(.neuroNexaText)
        }
        .padding()
        .frame(maxWidth: .infinity)
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
    }
}

@available(iOS 18.0, *)
struct ActivityCard: View {
    let activity: RecentActivity
    
    var body: some View {
        HStack {
            Image(systemName: activity.icon)
                .font(.title3)
                .foregroundColor(.neuroNexaPrimary)
                .frame(width: 24)
                .accessibilityLabel("\(activity.type) activity")
            
            VStack(alignment: .leading, spacing: 2) {
                Text(activity.title)
                    .font(.body)
                    .fontWeight(.medium)
                    .foregroundColor(.neuroNexaText)
                
                Text(activity.timestamp, style: .relative)
                    .font(.caption)
                    .foregroundColor(.neuroNexaTextSecondary)
            }
            
            Spacer()
        }
        .padding()
        .background(Color.neuroNexaSurface)
        .cornerRadius(12)
    }
}

#Preview {
    DashboardView()
}
