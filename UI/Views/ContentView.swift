import SwiftUI

@available(iOS 18.0, *)
struct ContentView: View {
    @EnvironmentObject var appState: AppState
    @EnvironmentObject var neuroAccessibilityManager: NeuroAccessibilityManager
    @State private var selectedTab = 0
    @State private var showingOnboarding = false

    var body: some View {
        Group {
            if appState.isAuthenticated {
                MainTabView(selectedTab: $selectedTab)
            } else {
                AuthenticationView()
            }
        }
        .sheet(isPresented: $showingOnboarding) {
            OnboardingView()
        }
        .onAppear {
            checkOnboardingStatus()
        }
        .accessibilityEnhanced()
        .cognitiveLoadOptimized()
        .sensoryAdaptive()
    }

    private func checkOnboardingStatus() {
        let hasCompletedOnboarding = UserDefaults.standard.bool(forKey: "HasCompletedOnboarding")
        if !hasCompletedOnboarding {
            showingOnboarding = true
        }
    }
}

@available(iOS 18.0, *)
struct MainTabView: View {
    @Binding var selectedTab: Int
    @EnvironmentObject var appState: AppState
    @EnvironmentObject var neuroAccessibilityManager: NeuroAccessibilityManager

    var body: some View {
        TabView(selection: $selectedTab) {
            // Dashboard
            DashboardView()
                .tabItem {
                    Image(systemName: "house.fill")
                        .accessibilityLabel("Dashboard")
                    Text("Dashboard")
                }
                .tag(0)

            // AI Task Coach
            AITaskCoachView(viewModel: AITaskCoachViewModel(
                aiTaskCoachService: AITaskCoachService(),
                cognitiveLoadService: CognitiveLoadService(),
                userRepository: UserRepository(),
                taskRepository: TaskRepository()
            ))
            .tabItem {
                Image(systemName: "brain.head.profile")
                    .accessibilityLabel("AI Coach")
                Text("AI Coach")
            }
            .tag(1)

            // Breathing Exercises
            AdvancedBreathingView(viewModel: BreathingViewModel(
                breathingService: BreathingExerciseService(
                    healthKitService: HealthKitService(),
                    watchConnectivityService: WatchConnectivityService(),
                    sensoryAdaptationService: SensoryAdaptationService()
                ),
                userRepository: UserRepository()
            ))
            .tabItem {
                Image(systemName: "lungs.fill")
                Text("Breathe")
            }
            .tag(2)
            .accessibilityLabel("Breathing exercises tab")

            // Routine Builder
            RoutineBuilderView(viewModel: RoutineBuilderViewModel())
                .tabItem {
                    Image(systemName: "calendar")
                    Text("Routines")
                }
                .tag(3)
                .accessibilityLabel("Routine builder tab")

            // Settings
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("Settings")
                }
                .tag(4)
                .accessibilityLabel("Settings tab")
        }
        .accentColor(.neuroNexaPrimary)
        .cognitiveLoadAdaptive()
    }
}
